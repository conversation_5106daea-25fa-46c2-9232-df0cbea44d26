<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="settings_record" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\settings_record.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/settings_record_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="171" endOffset="12"/></Target><Target id="@+id/resolution_group" view="RadioGroup"><Expressions/><location startLine="28" startOffset="12" endLine="46" endOffset="24"/></Target><Target id="@+id/radio_1080p" view="RadioButton"><Expressions/><location startLine="35" startOffset="16" endLine="39" endOffset="54"/></Target><Target id="@+id/radio_4k" view="RadioButton"><Expressions/><location startLine="41" startOffset="16" endLine="45" endOffset="51"/></Target><Target id="@+id/quality_group" view="RadioGroup"><Expressions/><location startLine="65" startOffset="12" endLine="94" endOffset="24"/></Target><Target id="@+id/radio_low" view="RadioButton"><Expressions/><location startLine="73" startOffset="16" endLine="78" endOffset="38"/></Target><Target id="@+id/radio_medium" view="RadioButton"><Expressions/><location startLine="80" startOffset="16" endLine="86" endOffset="44"/></Target><Target id="@+id/radio_high" view="RadioButton"><Expressions/><location startLine="88" startOffset="16" endLine="93" endOffset="38"/></Target><Target id="@+id/mode_group" view="RadioGroup"><Expressions/><location startLine="113" startOffset="12" endLine="132" endOffset="24"/></Target><Target id="@+id/radio_normal" view="RadioButton"><Expressions/><location startLine="120" startOffset="16" endLine="125" endOffset="44"/></Target><Target id="@+id/radio_low_latency" view="RadioButton"><Expressions/><location startLine="127" startOffset="16" endLine="131" endOffset="42"/></Target><Target id="@+id/btnApplySettings" view="Button"><Expressions/><location startLine="153" startOffset="12" endLine="159" endOffset="47"/></Target><Target id="@+id/btnResetDefault" view="Button"><Expressions/><location startLine="161" startOffset="12" endLine="167" endOffset="54"/></Target></Targets></Layout>