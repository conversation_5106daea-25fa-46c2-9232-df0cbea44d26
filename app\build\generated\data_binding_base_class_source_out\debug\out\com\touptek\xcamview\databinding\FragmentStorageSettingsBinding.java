// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentStorageSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnBrowse;

  @NonNull
  public final Button btnTestConnection;

  @NonNull
  public final CheckBox cbImageTimeSuffix;

  @NonNull
  public final CheckBox cbSmbEnable;

  @NonNull
  public final CheckBox cbVideoTimeSuffix;

  @NonNull
  public final EditText etImagePrefix;

  @NonNull
  public final EditText etPassword;

  @NonNull
  public final EditText etServerIp;

  @NonNull
  public final EditText etShareName;

  @NonNull
  public final EditText etUsername;

  @NonNull
  public final EditText etVideoPrefix;

  @NonNull
  public final RadioButton radioExternal;

  @NonNull
  public final RadioButton radioInternal;

  @NonNull
  public final Spinner spRemotePath;

  @NonNull
  public final RadioGroup storageLocationGroup;

  @NonNull
  public final TextView tvLastUpload;

  @NonNull
  public final TextView tvUploadStatus;

  private FragmentStorageSettingsBinding(@NonNull ScrollView rootView, @NonNull Button btnBrowse,
      @NonNull Button btnTestConnection, @NonNull CheckBox cbImageTimeSuffix,
      @NonNull CheckBox cbSmbEnable, @NonNull CheckBox cbVideoTimeSuffix,
      @NonNull EditText etImagePrefix, @NonNull EditText etPassword, @NonNull EditText etServerIp,
      @NonNull EditText etShareName, @NonNull EditText etUsername, @NonNull EditText etVideoPrefix,
      @NonNull RadioButton radioExternal, @NonNull RadioButton radioInternal,
      @NonNull Spinner spRemotePath, @NonNull RadioGroup storageLocationGroup,
      @NonNull TextView tvLastUpload, @NonNull TextView tvUploadStatus) {
    this.rootView = rootView;
    this.btnBrowse = btnBrowse;
    this.btnTestConnection = btnTestConnection;
    this.cbImageTimeSuffix = cbImageTimeSuffix;
    this.cbSmbEnable = cbSmbEnable;
    this.cbVideoTimeSuffix = cbVideoTimeSuffix;
    this.etImagePrefix = etImagePrefix;
    this.etPassword = etPassword;
    this.etServerIp = etServerIp;
    this.etShareName = etShareName;
    this.etUsername = etUsername;
    this.etVideoPrefix = etVideoPrefix;
    this.radioExternal = radioExternal;
    this.radioInternal = radioInternal;
    this.spRemotePath = spRemotePath;
    this.storageLocationGroup = storageLocationGroup;
    this.tvLastUpload = tvLastUpload;
    this.tvUploadStatus = tvUploadStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentStorageSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentStorageSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_storage_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentStorageSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_browse;
      Button btnBrowse = ViewBindings.findChildViewById(rootView, id);
      if (btnBrowse == null) {
        break missingId;
      }

      id = R.id.btn_test_connection;
      Button btnTestConnection = ViewBindings.findChildViewById(rootView, id);
      if (btnTestConnection == null) {
        break missingId;
      }

      id = R.id.cb_image_time_suffix;
      CheckBox cbImageTimeSuffix = ViewBindings.findChildViewById(rootView, id);
      if (cbImageTimeSuffix == null) {
        break missingId;
      }

      id = R.id.cb_smb_enable;
      CheckBox cbSmbEnable = ViewBindings.findChildViewById(rootView, id);
      if (cbSmbEnable == null) {
        break missingId;
      }

      id = R.id.cb_video_time_suffix;
      CheckBox cbVideoTimeSuffix = ViewBindings.findChildViewById(rootView, id);
      if (cbVideoTimeSuffix == null) {
        break missingId;
      }

      id = R.id.et_image_prefix;
      EditText etImagePrefix = ViewBindings.findChildViewById(rootView, id);
      if (etImagePrefix == null) {
        break missingId;
      }

      id = R.id.et_password;
      EditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.et_server_ip;
      EditText etServerIp = ViewBindings.findChildViewById(rootView, id);
      if (etServerIp == null) {
        break missingId;
      }

      id = R.id.et_share_name;
      EditText etShareName = ViewBindings.findChildViewById(rootView, id);
      if (etShareName == null) {
        break missingId;
      }

      id = R.id.et_username;
      EditText etUsername = ViewBindings.findChildViewById(rootView, id);
      if (etUsername == null) {
        break missingId;
      }

      id = R.id.et_video_prefix;
      EditText etVideoPrefix = ViewBindings.findChildViewById(rootView, id);
      if (etVideoPrefix == null) {
        break missingId;
      }

      id = R.id.radio_external;
      RadioButton radioExternal = ViewBindings.findChildViewById(rootView, id);
      if (radioExternal == null) {
        break missingId;
      }

      id = R.id.radio_internal;
      RadioButton radioInternal = ViewBindings.findChildViewById(rootView, id);
      if (radioInternal == null) {
        break missingId;
      }

      id = R.id.sp_remote_path;
      Spinner spRemotePath = ViewBindings.findChildViewById(rootView, id);
      if (spRemotePath == null) {
        break missingId;
      }

      id = R.id.storage_location_group;
      RadioGroup storageLocationGroup = ViewBindings.findChildViewById(rootView, id);
      if (storageLocationGroup == null) {
        break missingId;
      }

      id = R.id.tv_last_upload;
      TextView tvLastUpload = ViewBindings.findChildViewById(rootView, id);
      if (tvLastUpload == null) {
        break missingId;
      }

      id = R.id.tv_upload_status;
      TextView tvUploadStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvUploadStatus == null) {
        break missingId;
      }

      return new FragmentStorageSettingsBinding((ScrollView) rootView, btnBrowse, btnTestConnection,
          cbImageTimeSuffix, cbSmbEnable, cbVideoTimeSuffix, etImagePrefix, etPassword, etServerIp,
          etShareName, etUsername, etVideoPrefix, radioExternal, radioInternal, spRemotePath,
          storageLocationGroup, tvLastUpload, tvUploadStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
