// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.ui.TpRoiView;
import com.touptek.xcamview.R;
import com.touptek.xcamview.view.MeasurementOverlayView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView_;

  @NonNull
  public final TextureView blueTextureView;

  @NonNull
  public final TextView mainCenterInfoLabel;

  @NonNull
  public final MeasurementOverlayView measurementView;

  @NonNull
  public final TpRoiView roiView;

  @NonNull
  public final RelativeLayout rootView;

  @NonNull
  public final TextView tvDuration;

  @NonNull
  public final TextView tvTimer;

  @NonNull
  public final TextureView tvView;

  private ActivityMainBinding(@NonNull RelativeLayout rootView_,
      @NonNull TextureView blueTextureView, @NonNull TextView mainCenterInfoLabel,
      @NonNull MeasurementOverlayView measurementView, @NonNull TpRoiView roiView,
      @NonNull RelativeLayout rootView, @NonNull TextView tvDuration, @NonNull TextView tvTimer,
      @NonNull TextureView tvView) {
    this.rootView_ = rootView_;
    this.blueTextureView = blueTextureView;
    this.mainCenterInfoLabel = mainCenterInfoLabel;
    this.measurementView = measurementView;
    this.roiView = roiView;
    this.rootView = rootView;
    this.tvDuration = tvDuration;
    this.tvTimer = tvTimer;
    this.tvView = tvView;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView_;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.blue_texture_view;
      TextureView blueTextureView = ViewBindings.findChildViewById(rootView, id);
      if (blueTextureView == null) {
        break missingId;
      }

      id = R.id.main_center_info_label;
      TextView mainCenterInfoLabel = ViewBindings.findChildViewById(rootView, id);
      if (mainCenterInfoLabel == null) {
        break missingId;
      }

      id = R.id.measurement_view;
      MeasurementOverlayView measurementView = ViewBindings.findChildViewById(rootView, id);
      if (measurementView == null) {
        break missingId;
      }

      id = R.id.roi_view;
      TpRoiView roiView = ViewBindings.findChildViewById(rootView, id);
      if (roiView == null) {
        break missingId;
      }

      RelativeLayout rootView_ = (RelativeLayout) rootView;

      id = R.id.tv_duration;
      TextView tvDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvDuration == null) {
        break missingId;
      }

      id = R.id.tv_timer;
      TextView tvTimer = ViewBindings.findChildViewById(rootView, id);
      if (tvTimer == null) {
        break missingId;
      }

      id = R.id.tv_view;
      TextureView tvView = ViewBindings.findChildViewById(rootView, id);
      if (tvView == null) {
        break missingId;
      }

      return new ActivityMainBinding((RelativeLayout) rootView, blueTextureView,
          mainCenterInfoLabel, measurementView, roiView, rootView_, tvDuration, tvTimer, tvView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
