package com.touptek.xcamview.activity.settings
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.activity.MainActivity
import com.touptek.xcamview.util.BaseDialogFragment
import kotlin.apply
import kotlin.collections.forEach
import kotlin.let
import kotlin.to

class TpSettingsDialogFragment : BaseDialogFragment(), TpMiscSettingsFragment.OnModeChangeListener {
    private var currentTabId = R.id.item_format
    private var isCameraMode = false
    private var modeButton: Button? = null
    private var currentFragment: Fragment? = null

    override fun onSwitchToTVMode() {
            startTVMode()
    }

    companion object {
        const val TAG_NETWORK = "network_fragment"
        const val TAG_STORAGE = "storage_fragment"
        const val TAG_FORMAT = "format_fragment"
        const val TAG_VIDEO = "video_fragment"
        const val TAG_MEASUREMENT = "measurement_fragment"
        const val TAG_MISC = "misc_fragment"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.testdialog_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 恢复状态
        savedInstanceState?.let {
            currentTabId = it.getInt("CURRENT_TAB", R.id.item_format)
        }

        // 初始化标签选择
        setupTabSelection(view)

        // 设置初始内容
        switchTab(currentTabId, savedInstanceState == null)

        // 关闭按钮
        view.findViewById<ImageButton>(R.id.btn_close).setOnClickListener {
            dismiss()
        }

        // 模式切换按钮
        modeButton = view.findViewById<Button>(R.id.test_button)
        modeButton?.setOnClickListener {
            startTVMode()
        }
    }

    private fun setupTabSelection(view: View) {
        val netTab = view.findViewById<LinearLayout>(R.id.network_tab_container)
        val storageTab = view.findViewById<LinearLayout>(R.id.storage_tab_container)
        val formatTab = view.findViewById<LinearLayout>(R.id.format_tab_container)
        val videoTab = view.findViewById<LinearLayout>(R.id.video_tab_container)
        val measurementTab = view.findViewById<LinearLayout>(R.id.measurement_tab_container)
        val miscTab = view.findViewById<LinearLayout>(R.id.misc_tab_container)

        val tabs = mapOf(
            R.id.item_network to netTab,
            R.id.item_storage to storageTab,
            R.id.item_format to formatTab,
            R.id.item_video to videoTab,
            R.id.item_measurement to measurementTab,
            R.id.item_misc to miscTab
        )

        tabs.forEach { (id, container) ->
            container.setOnClickListener {
                switchTab(id, true)
            }
        }
    }

    private fun switchTab(tabId: Int, switchFragment: Boolean) {
        currentTabId = tabId

        // 更新所有标签的选中状态
        updateTabSelection()

        if (switchFragment) {
            // 切换对应的Fragment
            when (tabId) {
                R.id.item_network -> showFragment(TpNetworkSettingsFragment(), TAG_NETWORK)
                R.id.item_storage -> showFragment(TpStorageSettingsFragment(), TAG_STORAGE)
                R.id.item_format -> showFragment(TpFormatSettingsFragment(), TAG_FORMAT)
                R.id.item_video -> showFragment(TpRecordSettingsFragment(), TAG_VIDEO)
                R.id.item_measurement -> showFragment(TpMeasurementSettingsFragment(), TAG_MEASUREMENT)
                R.id.item_misc -> showFragment(TpMiscSettingsFragment(), TAG_MISC)
//                R.id.item_misc -> showFragment(TpMeasurementSettingsFragment(), TAG_MISC)
            }
        }
    }

    private fun updateTabSelection() {
        view?.let { rootView ->
            val networkTab = rootView.findViewById<LinearLayout>(R.id.network_tab_container)
            val storageTab = rootView.findViewById<LinearLayout>(R.id.storage_tab_container)
            val formatTab = rootView.findViewById<LinearLayout>(R.id.format_tab_container)
            val videoTab = rootView.findViewById<LinearLayout>(R.id.video_tab_container)
            val measurementTab = rootView.findViewById<LinearLayout>(R.id.measurement_tab_container)
            val miscTab = rootView.findViewById<LinearLayout>(R.id.misc_tab_container)

            val tabs = listOf(networkTab,storageTab,formatTab, videoTab, measurementTab, miscTab)

            tabs.forEach { tab ->
                val isSelected = when (tab.id) {
                    R.id.network_tab_container -> currentTabId == R.id.item_network
                    R.id.storage_tab_container -> currentTabId == R.id.item_storage
                    R.id.format_tab_container -> currentTabId == R.id.item_format
                    R.id.video_tab_container -> currentTabId == R.id.item_video
                    R.id.measurement_tab_container -> currentTabId == R.id.item_measurement
                    R.id.misc_tab_container -> currentTabId == R.id.item_misc
                    else -> false
                }
                tab.isSelected = isSelected

                // 更新文字颜色
                val textView = tab.findViewById<TextView>(when (tab.id) {
                    R.id.network_tab_container -> R.id.item_network
                    R.id.storage_tab_container -> R.id.item_storage
                    R.id.format_tab_container -> R.id.item_format
                    R.id.video_tab_container -> R.id.item_video
                    R.id.measurement_tab_container -> R.id.item_measurement
                    R.id.misc_tab_container -> R.id.item_misc
                    else -> 0
                })
                textView?.setTextColor(if (isSelected) Color.WHITE else Color.parseColor("#000000"))
            }
        }
    }

    private fun showFragment(fragment: Fragment, tag: String) {
        childFragmentManager.beginTransaction().apply {
            currentFragment?.let {
                if (it is TpMiscSettingsFragment) {
                    it.modeChangeListener = null // 解除旧绑定
                }
                if (it.isAdded) hide(it)
            }

            // 绑定新Fragment
            if (fragment is TpMiscSettingsFragment) {
                fragment.modeChangeListener = this@TpSettingsDialogFragment
            }

            val existingFragment = childFragmentManager.findFragmentByTag(tag)
            if (existingFragment != null) {
                show(existingFragment)
                currentFragment = existingFragment
            } else {
                replace(R.id.content_container, fragment, tag)
                currentFragment = fragment
            }
        }.commitAllowingStateLoss()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("CURRENT_TAB", currentTabId)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {

            clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND) // 清除默认遮罩效果


            // 窗口属性设置
            val params = attributes.apply {
                val displayMetrics = resources.displayMetrics
                val screenWidth = displayMetrics.widthPixels
                val screenHeight = displayMetrics.heightPixels

                // 设置窗口尺寸为屏幕的一半
                setLayout((screenWidth * 0.5).toInt(), (screenHeight * 0.5).toInt())

                // 设置窗口居中
                setGravity(Gravity.CENTER)
            }

            // 设置透明背景
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            attributes = params
        }
    }


    fun startTVMode(){
        isCameraMode = !isCameraMode  // 切换状态

        if(isCameraMode){
            (activity as? MainActivity)?.initTVMode()
            showToast("当前为TV模式")
        }else{
            (activity as? MainActivity)?.TvModeToCameraMode()
            showToast("当前为Camera模式")
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
}