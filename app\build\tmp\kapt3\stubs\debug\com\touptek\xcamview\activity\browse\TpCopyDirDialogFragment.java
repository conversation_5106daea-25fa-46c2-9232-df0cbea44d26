package com.touptek.xcamview.activity.browse;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u0000 .2\u00020\u0001:\u0002./B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\bH\u0002J\u0010\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\b\u0010\u0019\u001a\u00020\u0014H\u0002J\u0012\u0010\u001a\u001a\u00020\u00142\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0016J&\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\u0006\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\"2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0016J\b\u0010#\u001a\u00020\u0014H\u0016J\b\u0010$\u001a\u00020\u0014H\u0016J\u001a\u0010%\u001a\u00020\u00142\u0006\u0010&\u001a\u00020\u001e2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0016J\u0010\u0010\'\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\bH\u0002J\u000e\u0010(\u001a\u00020\u00142\u0006\u0010)\u001a\u00020\rJ\b\u0010*\u001a\u00020\u0014H\u0002J\u0010\u0010+\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\bH\u0002J\u0010\u0010,\u001a\u00020\u00142\u0006\u0010-\u001a\u00020\u000bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpCopyDirDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "adapter", "Lcom/touptek/xcamview/activity/browse/TpOperationDirAdapter;", "copyThread", "Ljava/lang/Thread;", "currentDir", "Ljava/io/File;", "filesToCopy", "", "", "moveCompleteListener", "Lcom/touptek/xcamview/activity/browse/TpCopyDirDialogFragment$OnMoveCompleteListener;", "progressDialog", "Landroid/app/ProgressDialog;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "rootDir", "copyFilesTo", "", "targetDir", "createProgressDialog", "max", "", "loadFolders", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroyView", "onStart", "onViewCreated", "view", "performDeleteOperation", "setOnMoveCompleteListener", "listener", "setupAdapter", "showDeleteConfirmationDialog", "showToast", "message", "Companion", "OnMoveCompleteListener", "app_debug"})
public final class TpCopyDirDialogFragment extends androidx.fragment.app.DialogFragment {
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private com.touptek.xcamview.activity.browse.TpOperationDirAdapter adapter;
    private java.io.File currentDir;
    private java.io.File rootDir;
    private java.util.List<java.lang.String> filesToCopy;
    private java.lang.Thread copyThread;
    private android.app.ProgressDialog progressDialog;
    private com.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListener moveCompleteListener;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.Companion Companion = null;
    private static final java.lang.String KEY_ROOT_DIR = "root_dir";
    private static final java.lang.String KEY_CURRENT_DIR = "current_dir";
    private static final java.lang.String KEY_COPY_FILES = "copy_files";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String OPERATION_COPY = "copy";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String OPERATION_CUT = "cut";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String OPERATION_DELETE = "delete";
    private static final java.lang.String KEY_OPERATION_TYPE = "operation_type";
    private static java.lang.String operationType = "copy";
    
    public TpCopyDirDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupAdapter() {
    }
    
    private final void loadFolders() {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void copyFilesTo(java.io.File targetDir) {
    }
    
    private final android.app.ProgressDialog createProgressDialog(int max) {
        return null;
    }
    
    private final void showDeleteConfirmationDialog(java.io.File targetDir) {
    }
    
    private final void performDeleteOperation(java.io.File targetDir) {
    }
    
    @java.lang.Override
    public void onDestroyView() {
    }
    
    public final void setOnMoveCompleteListener(@org.jetbrains.annotations.NotNull
    com.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListener listener) {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpCopyDirDialogFragment$OnMoveCompleteListener;", "", "onMoveComplete", "", "success", "", "app_debug"})
    public static abstract interface OnMoveCompleteListener {
        
        public abstract void onMoveComplete(boolean success);
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J,\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u00122\u0006\u0010\u000b\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpCopyDirDialogFragment$Companion;", "", "()V", "KEY_COPY_FILES", "", "KEY_CURRENT_DIR", "KEY_OPERATION_TYPE", "KEY_ROOT_DIR", "OPERATION_COPY", "OPERATION_CUT", "OPERATION_DELETE", "operationType", "newInstance", "Lcom/touptek/xcamview/activity/browse/TpCopyDirDialogFragment;", "rootDir", "Ljava/io/File;", "currentDir", "filePaths", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.browse.TpCopyDirDialogFragment newInstance(@org.jetbrains.annotations.NotNull
        java.io.File rootDir, @org.jetbrains.annotations.NotNull
        java.io.File currentDir, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> filePaths, @org.jetbrains.annotations.NotNull
        java.lang.String operationType) {
            return null;
        }
    }
}