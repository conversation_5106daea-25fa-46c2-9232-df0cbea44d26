<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- 图片显示区域 -->
    <com.touptek.ui.TpImageView
        android:id="@+id/image_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 图片信息标签 - 现代化设计 -->
    <LinearLayout
        android:id="@+id/image_info_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|top"
        android:layout_margin="20dp"
        android:orientation="vertical"
        android:background="@drawable/image_info_label_bg"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:elevation="8dp"
        android:gravity="start"
        android:minWidth="120dp">

        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="15sp"
            android:textStyle="bold"
            android:gravity="start"
            android:maxLines="2"
            android:ellipsize="middle"
            android:maxWidth="200dp"
            android:lineSpacingExtra="2dp"/>

        <TextView
            android:id="@+id/tv_resolution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#E0FFFFFF"
            android:textSize="13sp"
            android:gravity="start"
            android:layout_marginTop="4dp"
            android:fontFamily="monospace"/>
    </LinearLayout>

    <!-- 悬浮导航按钮容器 -->
    <LinearLayout
        android:id="@+id/button_panel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:background="@drawable/floating_panel_bg"
        android:elevation="8dp">

        <!-- 上一页按钮 -->
        <ImageButton
            android:id="@+id/btn_previous"
            android:layout_width="68dp"
            android:layout_height="68dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/floating_nav_button_bg"
            android:src="@drawable/ic_nav_previous_large"
            android:scaleType="center"
            android:contentDescription="上一页"
            android:elevation="6dp" />

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="68dp"
            android:layout_height="68dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/floating_nav_button_bg"
            android:src="@drawable/ic_nav_back_large"
            android:scaleType="center"
            android:contentDescription="返回"
            android:elevation="6dp" />

        <!-- 下一页按钮 -->
        <ImageButton
            android:id="@+id/btn_next"
            android:layout_width="68dp"
            android:layout_height="68dp"
            android:layout_marginStart="20dp"
            android:background="@drawable/floating_nav_button_bg"
            android:src="@drawable/ic_nav_next_large"
            android:scaleType="center"
            android:contentDescription="下一页"
            android:elevation="6dp" />

    </LinearLayout>
</FrameLayout>