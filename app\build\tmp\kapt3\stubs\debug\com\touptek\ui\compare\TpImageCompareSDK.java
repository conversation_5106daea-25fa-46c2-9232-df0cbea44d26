package com.touptek.ui.compare;

import java.lang.System;

/**
 * 图片对比功能统一SDK入口
 *
 * 提供统一的API接口，根据图片数量自动选择合适的对比Activity
 * 支持2-4张图片的对比功能
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0007\u00a8\u0006\n"}, d2 = {"Lcom/touptek/ui/compare/TpImageCompareSDK;", "", "()V", "startCompare", "", "context", "Landroid/content/Context;", "imagePaths", "", "", "app_debug"})
public final class TpImageCompareSDK {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.ui.compare.TpImageCompareSDK INSTANCE = null;
    
    private TpImageCompareSDK() {
        super();
    }
    
    /**
     * 启动图片对比功能
     *
     * @param context 上下文
     * @param imagePaths 图片路径列表，支持2-4张图片
     */
    @kotlin.jvm.JvmStatic
    public static final void startCompare(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> imagePaths) {
    }
}