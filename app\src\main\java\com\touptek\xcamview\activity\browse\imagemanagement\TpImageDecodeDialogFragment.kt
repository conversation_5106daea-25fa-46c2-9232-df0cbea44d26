package com.touptek.xcamview.activity.browse.imagemanagement

import android.graphics.Matrix
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.xcamview.databinding.ImageViewerBinding
import com.touptek.ui.TpImageView
import com.touptek.video.internal.TpImageLoader
import java.util.ArrayList
import kotlin.apply
import kotlin.collections.indices
import kotlin.let
import kotlin.run

class TpImageDecodeDialogFragment : BaseDialogFragment() {
    private lateinit var binding: ImageViewerBinding
    private var buttonsVisible = false
    private lateinit var imagePaths: List<String>
    private var currentPosition = 0

    private var lastX = 0f
    private var lastY = 0f
    private val matrix = Matrix()
    private lateinit var scaleGestureDetector: ScaleGestureDetector

    private var startTime: Long = 0
    private var startX = 0f
    private var startY = 0f
    private val CLICK_DURATION = 300      // 最大点击持续时间（毫秒）
    private val LONG_PRESS_DURATION = 200 // 长按持续时间（毫秒）
    private val SWIPE_THRESHOLD = 50      // 滑动距离阈值（像素）- 降低阈值
    private var isMoved = false
    private var isLongPress = false
    private var isSwipeGesture = false
    private val touchSlop: Int by lazy {
        ViewConfiguration.get(requireContext()).scaledTouchSlop
    }


    companion object {
        private const val ARG_IMAGE_PATHS = "image_paths"
        private const val ARG_CURRENT_POSITION = "current_position"

        fun newInstance(imagePaths: List<String>, currentPosition: Int): TpImageDecodeDialogFragment {
            val fragment = TpImageDecodeDialogFragment()
            val args = Bundle().apply {
                putStringArrayList(ARG_IMAGE_PATHS, ArrayList(imagePaths))
                putInt(ARG_CURRENT_POSITION, currentPosition)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ImageViewerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 获取传递的参数
        imagePaths = arguments?.getStringArrayList(ARG_IMAGE_PATHS) ?: run {
            dismiss()
            return
        }
        currentPosition = arguments?.getInt(ARG_CURRENT_POSITION, 0) ?: 0

        // 加载当前图片
        loadCurrentImage()

        // 初始化按钮面板为隐藏状态
        binding.buttonPanel.visibility = View.GONE

        // 设置TpImageView的单击监听器来切换按钮可见性
        binding.imageView.setOnSingleTapListener {
            toggleButtons()
        }

        // 按钮点击事件
        binding.btnPrevious.setOnClickListener {
            showPreviousImage()
        }

        binding.btnNext.setOnClickListener {
            showNextImage()
        }

        binding.btnBack.setOnClickListener {
            dismiss()
        }


        //Zoom
//        binding.imageView.scaleType = ImageView.ScaleType.MATRIX
//        initScaleTouchEvent(view)


    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog) // 全屏样式
    }

    override fun onStart() {
        super.onStart()
        // 设置对话框全屏
        dialog?.window?.let { window ->
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    private fun loadCurrentImage() {
        if (currentPosition in imagePaths.indices) {
            // 使用TpImageLoader加载高质量图片（用于详细查看）
            TpImageLoader.loadFullImage(imagePaths[currentPosition], binding.imageView)
            updateImageInfoLabel(imagePaths[currentPosition])
            updateButtonStates()
        }
    }

    private fun showPreviousImage() {
        if (currentPosition > 0) {
            currentPosition--
            loadCurrentImage()
        }
    }

    private fun showNextImage() {
        if (currentPosition < imagePaths.size - 1) {
            currentPosition++
            loadCurrentImage()
        }
    }

    private fun updateButtonStates() {
        // 更新上一页按钮状态
        binding.btnPrevious.isEnabled = currentPosition > 0
        binding.btnPrevious.alpha = if (currentPosition > 0) 1.0f else 0.5f

        // 更新下一页按钮状态
        binding.btnNext.isEnabled = currentPosition < imagePaths.size - 1
        binding.btnNext.alpha = if (currentPosition < imagePaths.size - 1) 1.0f else 0.5f

        // 更新标题或其他UI元素（如果需要）
        // 例如：binding.tvImageCounter.text = "${currentPosition + 1}/${imagePaths.size}"
    }

    private fun toggleButtons() {
        buttonsVisible = !buttonsVisible
        binding.buttonPanel.visibility = if (buttonsVisible) View.VISIBLE else View.GONE
    }

//
//    private fun initScaleGestureDetector() {
//        scaleGestureDetector = ScaleGestureDetector(
//            requireContext(),
//            object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
//                override fun onScale(detector: ScaleGestureDetector): Boolean {
//                    TpViewTransform.applyZoom(
//                        binding.imageView,
//                        matrix,
//                        detector.scaleFactor,
//                        detector.focusX,
//                        detector.focusY
//                    )
//                    return true
//                }
//            })
//    }

    private fun updateImageInfoLabel(imagePath: String) {
        try {
            // 使用BitmapFactory获取图片分辨率
            val options = android.graphics.BitmapFactory.Options()
            options.inJustDecodeBounds = true
            android.graphics.BitmapFactory.decodeFile(imagePath, options)

            // 更新文件名和分辨率显示
            binding.root.findViewById<android.widget.TextView>(R.id.tv_file_name)?.text =
                imagePath.substringAfterLast("/")

            binding.root.findViewById<android.widget.TextView>(R.id.tv_resolution)?.text =
                if (options.outWidth > 0 && options.outHeight > 0) {
                    "${options.outWidth} x ${options.outHeight}"
                } else {
                    "分辨率未知"
                }
        } catch (e: Exception) {
            android.util.Log.e("ImageInfo", "更新图片信息失败", e)
        }
    }

    private fun initScaleTouchEvent(view: View) {
        binding.imageView.setOnTouchListener { v, event ->
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    // 重置所有状态
                    isMoved = false
                    isLongPress = false
                    isSwipeGesture = false

                    // 记录初始位置和时间
                    lastX = event.x
                    lastY = event.y
                    startX = event.x
                    startY = event.y
                    startTime = System.currentTimeMillis()
                }
                MotionEvent.ACTION_MOVE -> {
                    val currentTime = System.currentTimeMillis()
                    val deltaX = event.x - startX
                    val deltaY = event.y - startY
                    val distance = Math.hypot(deltaX.toDouble(), deltaY.toDouble())

                    // 首先判断是否为长按（在移动之前）
                    if (!isLongPress && currentTime - startTime >= LONG_PRESS_DURATION) {
                        isLongPress = true
                    }

                    // 检测是否发生了有效移动
                    if (!isMoved && distance > touchSlop) {
                        isMoved = true
                        android.util.Log.d("TouchEvent", "检测到移动: distance=$distance, touchSlop=$touchSlop, isLongPress=$isLongPress")

                        // 如果不是长按，判断是否为水平滑动手势
                        if (!isLongPress) {
                            val absX = Math.abs(deltaX)
                            val absY = Math.abs(deltaY)
                            android.util.Log.d("TouchEvent", "判断滑动方向: absX=$absX, absY=$absY")
                            // 降低滑动识别要求：水平距离大于垂直距离即可
                            if (absX > absY) {
                                isSwipeGesture = true
                                android.util.Log.d("TouchEvent", "识别为水平滑动手势")
                            }
                        }
                    }

                    // 根据手势类型处理移动
                    if (isMoved) {
                        if (isLongPress) {
                            // 长按：执行图片平移
                            val panDeltaX = event.x - lastX
                            val panDeltaY = event.y - lastY
//                            TpViewTransform.applyPan(binding.imageView, matrix, panDeltaX, panDeltaY)

//                            换成新Move接口
                            lastX = event.x
                            lastY = event.y
                        }
                        // 短滑动手势在ACTION_UP中处理
                    }
                }
                MotionEvent.ACTION_UP -> {
                    val duration = System.currentTimeMillis() - startTime
                    val deltaX = event.x - startX

                    if (!isMoved) {
                        // 单击：切换按钮显示
                        if (duration < CLICK_DURATION) {
                            toggleButtons()
                        }
                    } else if (isSwipeGesture && !isLongPress) {
                        // 短滑动：检查滑动距离是否足够翻页
                        val absX = Math.abs(deltaX)
                        android.util.Log.d("TouchEvent", "检测到滑动手势: absX=$absX, threshold=$SWIPE_THRESHOLD, isSwipeGesture=$isSwipeGesture, isLongPress=$isLongPress")
                        if (absX > SWIPE_THRESHOLD) {
                            android.util.Log.d("TouchEvent", "滑动距离足够，执行翻页")
                            handleSwipeGesture(deltaX)
                        } else {
                            android.util.Log.d("TouchEvent", "滑动距离不足，不执行翻页")
                        }
                    }

                    // 重置状态
                    resetTouchState()
                }
                MotionEvent.ACTION_CANCEL -> {
                    // 重置状态
                    resetTouchState()
                }
            }

            // 将事件传递给缩放检测器
            scaleGestureDetector.onTouchEvent(event)
            true
        }
    }

    /**
     * 处理滑动手势翻页
     */
    private fun handleSwipeGesture(deltaX: Float) {
        android.util.Log.d("SwipeGesture", "处理滑动手势: deltaX=$deltaX, currentPosition=$currentPosition, totalImages=${imagePaths.size}")

        if (deltaX > 0) {
            // 向右滑动：上一页
            if (currentPosition > 0) {
                android.util.Log.d("SwipeGesture", "向右滑动 - 切换到上一页")
                showPreviousImage()
            } else {
                android.util.Log.d("SwipeGesture", "向右滑动 - 已经是第一页，无法切换")
            }
        } else {
            // 向左滑动：下一页
            if (currentPosition < imagePaths.size - 1) {
                android.util.Log.d("SwipeGesture", "向左滑动 - 切换到下一页")
                showNextImage()
            } else {
                android.util.Log.d("SwipeGesture", "向左滑动 - 已经是最后一页，无法切换")
            }
        }
    }

    /**
     * 重置触摸状态
     */
    private fun resetTouchState() {
        isMoved = false
        isLongPress = false
        isSwipeGesture = false
    }
}