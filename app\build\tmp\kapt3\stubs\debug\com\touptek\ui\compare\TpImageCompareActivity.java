package com.touptek.ui.compare;

import java.lang.System;

/**
 * 双图对比Activity
 *
 * 支持两张图片的并排对比，包括同步缩放、平移等功能
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010!\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 %2\u00020\u0001:\u0001%B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\nH\u0002J\b\u0010\u0017\u001a\u00020\u0018H\u0002J\b\u0010\u0019\u001a\u00020\u0018H\u0002J\b\u0010\u001a\u001a\u00020\u0018H\u0002J\u0012\u0010\u001b\u001a\u00020\u00182\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0014J\b\u0010\u001e\u001a\u00020\u0018H\u0014J\b\u0010\u001f\u001a\u00020\u0018H\u0002J\b\u0010 \u001a\u00020\u0018H\u0002J\b\u0010!\u001a\u00020\u0018H\u0002J\b\u0010\"\u001a\u00020\u0018H\u0002J\b\u0010#\u001a\u00020\u0018H\u0002J\b\u0010$\u001a\u00020\u0018H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/touptek/ui/compare/TpImageCompareActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnBack", "Landroid/widget/ImageButton;", "btnReset", "btnSwap", "btnSync", "imagePaths", "", "", "isSyncEnabled", "", "leftImageView", "Lcom/touptek/ui/TpImageView;", "rightImageView", "syncEngine", "Lcom/touptek/ui/compare/TpImageSyncEngine;", "tvLeftInfo", "Landroid/widget/TextView;", "tvRightInfo", "getImageResolution", "imagePath", "getIntentData", "", "initViews", "loadImages", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "setupClickListeners", "setupFullScreen", "setupSyncEngine", "swapImages", "updateImageInfo", "updateSyncButtonState", "Companion", "app_debug"})
public final class TpImageCompareActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.ui.compare.TpImageCompareActivity.Companion Companion = null;
    private static final java.lang.String TAG = "TpImageCompareActivity";
    private static final java.lang.String EXTRA_IMAGE_PATHS = "extra_image_paths";
    private com.touptek.ui.TpImageView leftImageView;
    private com.touptek.ui.TpImageView rightImageView;
    private android.widget.ImageButton btnBack;
    private android.widget.ImageButton btnSync;
    private android.widget.ImageButton btnReset;
    private android.widget.ImageButton btnSwap;
    private android.widget.TextView tvLeftInfo;
    private android.widget.TextView tvRightInfo;
    private java.util.List<java.lang.String> imagePaths;
    private com.touptek.ui.compare.TpImageSyncEngine syncEngine;
    private boolean isSyncEnabled = true;
    
    public TpImageCompareActivity() {
        super();
    }
    
    /**
     * 启动双图对比Activity
     */
    @kotlin.jvm.JvmStatic
    public static final void start(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> imagePaths) {
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupFullScreen() {
    }
    
    private final void initViews() {
    }
    
    private final void getIntentData() {
    }
    
    private final void setupSyncEngine() {
    }
    
    private final void loadImages() {
    }
    
    private final void updateImageInfo() {
    }
    
    private final java.lang.String getImageResolution(java.lang.String imagePath) {
        return null;
    }
    
    private final void setupClickListeners() {
    }
    
    private final void updateSyncButtonState() {
    }
    
    private final void swapImages() {
    }
    
    @java.lang.Override
    protected void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\u000bH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/ui/compare/TpImageCompareActivity$Companion;", "", "()V", "EXTRA_IMAGE_PATHS", "", "TAG", "start", "", "context", "Landroid/content/Context;", "imagePaths", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 启动双图对比Activity
         */
        @kotlin.jvm.JvmStatic
        public final void start(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> imagePaths) {
        }
    }
}