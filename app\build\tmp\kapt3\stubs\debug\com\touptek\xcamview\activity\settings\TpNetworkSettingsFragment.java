package com.touptek.xcamview.activity.settings;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u0000 .2\u00020\u0001:\u0001.B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J&\u0010\u001b\u001a\u0004\u0018\u00010\u001a2\u0006\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001f2\b\u0010 \u001a\u0004\u0018\u00010!H\u0016J\u001a\u0010\"\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010 \u001a\u0004\u0018\u00010!H\u0016J\b\u0010#\u001a\u00020\u0018H\u0002J\b\u0010$\u001a\u00020\u0018H\u0002J\b\u0010%\u001a\u00020\u0018H\u0002J\u0018\u0010&\u001a\u00020\u00182\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020(H\u0002J\u0018\u0010*\u001a\u00020\u00182\u0006\u0010\'\u001a\u00020(2\u0006\u0010+\u001a\u00020(H\u0002J\u0018\u0010,\u001a\u00020\u00182\u0006\u0010\'\u001a\u00020(2\u0006\u0010-\u001a\u00020(H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpNetworkSettingsFragment;", "Lcom/touptek/xcamview/util/BaseFragment;", "()V", "btnDisconnectEthernet", "Landroid/widget/Button;", "btnEthernetConfig", "btnStartStream", "btnStopStream", "btnToggleAp", "btnToggleWifi", "rgStreamType", "Landroid/widget/RadioGroup;", "spinnerNetworkInterface", "Landroid/widget/Spinner;", "tvApSsid", "Landroid/widget/TextView;", "tvApStatus", "tvCurrentWifi", "tvEthernetIp", "tvEthernetStatus", "tvRtspStatus", "tvRtspUri", "tvSTAStatus", "initViews", "", "view", "Landroid/view/View;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "setupEthernetSection", "setupRtspSection", "setupWifiSection", "updateApStatus", "status", "", "ssid", "updateEthernetStatus", "ip", "updateWifiStatus", "network", "Companion", "app_debug"})
public final class TpNetworkSettingsFragment extends com.touptek.xcamview.util.BaseFragment {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.settings.TpNetworkSettingsFragment.Companion Companion = null;
    private static final java.lang.String TAG = "NetworkSettings";
    private android.widget.TextView tvEthernetStatus;
    private android.widget.TextView tvEthernetIp;
    private android.widget.Button btnEthernetConfig;
    private android.widget.Button btnDisconnectEthernet;
    private android.widget.TextView tvSTAStatus;
    private android.widget.TextView tvCurrentWifi;
    private android.widget.Button btnToggleWifi;
    private android.widget.TextView tvApStatus;
    private android.widget.TextView tvApSsid;
    private android.widget.Button btnToggleAp;
    private android.widget.TextView tvRtspStatus;
    private android.widget.TextView tvRtspUri;
    private android.widget.Spinner spinnerNetworkInterface;
    private android.widget.RadioGroup rgStreamType;
    private android.widget.Button btnStartStream;
    private android.widget.Button btnStopStream;
    
    public TpNetworkSettingsFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupEthernetSection() {
    }
    
    private final void updateEthernetStatus(java.lang.String status, java.lang.String ip) {
    }
    
    private final void setupWifiSection() {
    }
    
    private final void updateWifiStatus(java.lang.String status, java.lang.String network) {
    }
    
    private final void updateApStatus(java.lang.String status, java.lang.String ssid) {
    }
    
    private final void setupRtspSection() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpNetworkSettingsFragment$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}