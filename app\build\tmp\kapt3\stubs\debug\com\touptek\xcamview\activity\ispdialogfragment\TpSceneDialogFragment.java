package com.touptek.xcamview.activity.ispdialogfragment;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 &2\u00020\u0001:\u0001&B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\bH\u0002J\u0010\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\bH\u0002J\u0010\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\bH\u0002J\b\u0010\u0012\u001a\u00020\bH\u0002J\b\u0010\u0013\u001a\u00020\u0014H\u0014J\u0010\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J&\u0010\u0018\u001a\u0004\u0018\u00010\u00172\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0016J\b\u0010\u001f\u001a\u00020\u000eH\u0016J\b\u0010 \u001a\u00020\u000eH\u0002J\b\u0010!\u001a\u00020\u000eH\u0002J\b\u0010\"\u001a\u00020\u000eH\u0002J\b\u0010#\u001a\u00020\u000eH\u0002J\b\u0010$\u001a\u00020\u000eH\u0002J\u0010\u0010%\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/TpSceneDialogFragment;", "Lcom/touptek/xcamview/activity/ispdialogfragment/BaseISPDialogFragment;", "()V", "addButton", "Landroid/widget/Button;", "deleteButton", "sceneAdapter", "Landroid/widget/ArrayAdapter;", "", "sceneList", "", "sceneSpinner", "Landroid/widget/Spinner;", "addNewScene", "", "sceneName", "applyScene", "deleteScene", "getCurrentSelectedScene", "getDialogHeight", "", "initViews", "view", "Landroid/view/View;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onStart", "refreshSceneList", "setupButtons", "setupSpinner", "showAddSceneDialog", "showDeleteSceneDialog", "updateDeleteButtonState", "Companion", "app_debug"})
public final class TpSceneDialogFragment extends com.touptek.xcamview.activity.ispdialogfragment.BaseISPDialogFragment {
    private android.widget.Spinner sceneSpinner;
    private android.widget.Button addButton;
    private android.widget.Button deleteButton;
    private android.widget.ArrayAdapter<java.lang.String> sceneAdapter;
    private java.util.List<java.lang.String> sceneList;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.ispdialogfragment.TpSceneDialogFragment.Companion Companion = null;
    private static final java.lang.String TAG = "TpSceneDialogFragment";
    private static final java.util.List<java.lang.String> SYSTEM_SCENES = null;
    
    public TpSceneDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupSpinner() {
    }
    
    private final void setupButtons() {
    }
    
    private final void showAddSceneDialog() {
    }
    
    private final void showDeleteSceneDialog() {
    }
    
    private final void addNewScene(java.lang.String sceneName) {
    }
    
    private final void deleteScene(java.lang.String sceneName) {
    }
    
    private final void applyScene(java.lang.String sceneName) {
    }
    
    private final void refreshSceneList() {
    }
    
    private final java.lang.String getCurrentSelectedScene() {
        return null;
    }
    
    private final void updateDeleteButtonState(java.lang.String sceneName) {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    @java.lang.Override
    protected int getDialogHeight() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/TpSceneDialogFragment$Companion;", "", "()V", "SYSTEM_SCENES", "", "", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}