// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class HzLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioGroup hzBtnGroup;

  @NonNull
  public final RadioButton radioAc50hzTv;

  @NonNull
  public final RadioButton radioAc60hzTv;

  @NonNull
  public final RadioButton radioDcTv;

  private HzLayoutBinding(@NonNull LinearLayout rootView, @NonNull RadioGroup hzBtnGroup,
      @NonNull RadioButton radioAc50hzTv, @NonNull RadioButton radioAc60hzTv,
      @NonNull RadioButton radioDcTv) {
    this.rootView = rootView;
    this.hzBtnGroup = hzBtnGroup;
    this.radioAc50hzTv = radioAc50hzTv;
    this.radioAc60hzTv = radioAc60hzTv;
    this.radioDcTv = radioDcTv;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static HzLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static HzLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.hz_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static HzLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.hz_btn_group;
      RadioGroup hzBtnGroup = ViewBindings.findChildViewById(rootView, id);
      if (hzBtnGroup == null) {
        break missingId;
      }

      id = R.id.radio_ac_50hz_tv;
      RadioButton radioAc50hzTv = ViewBindings.findChildViewById(rootView, id);
      if (radioAc50hzTv == null) {
        break missingId;
      }

      id = R.id.radio_ac_60hz_tv;
      RadioButton radioAc60hzTv = ViewBindings.findChildViewById(rootView, id);
      if (radioAc60hzTv == null) {
        break missingId;
      }

      id = R.id.radio_dc_tv;
      RadioButton radioDcTv = ViewBindings.findChildViewById(rootView, id);
      if (radioDcTv == null) {
        break missingId;
      }

      return new HzLayoutBinding((LinearLayout) rootView, hzBtnGroup, radioAc50hzTv, radioAc60hzTv,
          radioDcTv);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
