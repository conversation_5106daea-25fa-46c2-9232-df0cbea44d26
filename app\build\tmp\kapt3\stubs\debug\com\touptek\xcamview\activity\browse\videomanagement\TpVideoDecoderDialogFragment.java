package com.touptek.xcamview.activity.browse.videomanagement;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0092\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\u0015\n\u0002\b\f\u0018\u0000 A2\u00020\u0001:\u0001AB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u000eH\u0002J\u0012\u0010%\u001a\u00020&2\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J&\u0010)\u001a\u0004\u0018\u00010*2\u0006\u0010+\u001a\u00020,2\b\u0010-\u001a\u0004\u0018\u00010.2\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J\b\u0010/\u001a\u00020&H\u0016J-\u00100\u001a\u00020&2\u0006\u00101\u001a\u0002022\u000e\u00103\u001a\n\u0012\u0006\b\u0001\u0012\u00020#042\u0006\u00105\u001a\u000206H\u0016\u00a2\u0006\u0002\u00107J\u001a\u00108\u001a\u00020&2\u0006\u00109\u001a\u00020*2\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J\b\u0010:\u001a\u00020&H\u0002J\b\u0010;\u001a\u00020&H\u0002J\b\u0010<\u001a\u00020&H\u0002J\b\u0010=\u001a\u00020&H\u0002J\u0010\u0010>\u001a\u00020&2\u0006\u0010?\u001a\u00020\fH\u0002J\b\u0010@\u001a\u00020&H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000eX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000eX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006B"}, d2 = {"Lcom/touptek/xcamview/activity/browse/videomanagement/TpVideoDecoderDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "fastBackwardButton", "Landroid/widget/Button;", "fastForwardButton", "frameByFrameButton", "gestureDetector", "Landroid/view/GestureDetector;", "handler", "Landroid/os/Handler;", "isLongPress", "", "lastSeekPosition", "", "lastSeekUpdateTime", "longPressRunnable", "Ljava/lang/Runnable;", "longPressThreshold", "playPauseButton", "repeatInterval", "returnbtn", "scaleGestureDetector", "Landroid/view/ScaleGestureDetector;", "seekBar", "Landroid/widget/SeekBar;", "textureView", "Landroid/view/TextureView;", "tpVideoDecoder", "Lcom/touptek/video/internal/TpVideoDecoder;", "tvCurrentPosition", "Landroid/widget/TextView;", "tvDuration", "updateSeekBarRunnable", "formatTime", "", "timeUs", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroyView", "onRequestPermissionsResult", "requestCode", "", "permissions", "", "grantResults", "", "(I[Ljava/lang/String;[I)V", "onViewCreated", "view", "requestStoragePermission", "setupControls", "setupVideoPlayback", "startRepeatingStep", "updatePlayButton", "playing", "updatePlayerUI", "Companion", "app_debug"})
public final class TpVideoDecoderDialogFragment extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.browse.videomanagement.TpVideoDecoderDialogFragment.Companion Companion = null;
    private static final java.lang.String TAG = "VideoDecoderDialog";
    private static final java.lang.String ARG_VIDEO_PATH = "video_path";
    private android.view.TextureView textureView;
    private android.widget.SeekBar seekBar;
    private android.widget.Button playPauseButton;
    private android.widget.Button frameByFrameButton;
    private android.widget.Button fastForwardButton;
    private android.widget.Button fastBackwardButton;
    private android.widget.Button returnbtn;
    private android.widget.TextView tvDuration;
    private android.widget.TextView tvCurrentPosition;
    private com.touptek.video.internal.TpVideoDecoder tpVideoDecoder;
    private final android.os.Handler handler = null;
    private long lastSeekPosition = 0L;
    private long lastSeekUpdateTime = 0L;
    private android.view.ScaleGestureDetector scaleGestureDetector;
    private android.view.GestureDetector gestureDetector;
    private boolean isLongPress = false;
    private final long longPressThreshold = 500L;
    private final long repeatInterval = 50L;
    private final java.lang.Runnable longPressRunnable = null;
    private final java.lang.Runnable updateSeekBarRunnable = null;
    
    public TpVideoDecoderDialogFragment() {
        super();
    }
    
    @java.lang.Override
    public void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupVideoPlayback() {
    }
    
    private final void setupControls() {
    }
    
    private final void requestStoragePermission() {
    }
    
    @java.lang.Override
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull
    int[] grantResults) {
    }
    
    private final void updatePlayButton(boolean playing) {
    }
    
    private final java.lang.String formatTime(long timeUs) {
        return null;
    }
    
    @java.lang.Override
    public void onDestroyView() {
    }
    
    private final void updatePlayerUI() {
    }
    
    private final void startRepeatingStep() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/touptek/xcamview/activity/browse/videomanagement/TpVideoDecoderDialogFragment$Companion;", "", "()V", "ARG_VIDEO_PATH", "", "TAG", "newInstance", "Lcom/touptek/xcamview/activity/browse/videomanagement/TpVideoDecoderDialogFragment;", "videoPath", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.browse.videomanagement.TpVideoDecoderDialogFragment newInstance(@org.jetbrains.annotations.NotNull
        java.lang.String videoPath) {
            return null;
        }
    }
}