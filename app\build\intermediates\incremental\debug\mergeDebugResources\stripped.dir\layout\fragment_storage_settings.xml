    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 存储位置设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/border_box"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="存储位置"
                android:textSize="22sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <RadioGroup
                android:id="@+id/storage_location_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio_external"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="外部存储（U盘）"
                    android:layout_marginBottom="8dp"/>

                <RadioButton
                    android:id="@+id/radio_internal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="内部存储（本地）"/>
            </RadioGroup>
        </LinearLayout>

        <!-- SMB服务器备份设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/border_box"
            android:padding="16dp"
            android:layout_marginTop="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="SMB服务器备份"
                android:textSize="22sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp"/>

            <CheckBox
                android:id="@+id/cb_smb_enable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="启用SMB自动上传"
                android:layout_marginBottom="16dp"/>

            <!-- 服务器IP -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="服务器IP："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <EditText
                    android:id="@+id/et_server_ip"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="2"
                    android:inputType="text"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"/>
            </LinearLayout>

            <!-- 共享名称 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="共享名称："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <EditText
                    android:id="@+id/et_share_name"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="2"
                    android:inputType="text"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"/>
            </LinearLayout>

            <!-- 用户名 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="用户名："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <EditText
                    android:id="@+id/et_username"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="2"
                    android:inputType="text"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"/>
            </LinearLayout>

            <!-- 密码 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="密码："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <EditText
                    android:id="@+id/et_password"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1.5"
                    android:inputType="textPassword"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"/>

                <Button
                    android:id="@+id/btn_test_connection"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_weight="0.5"
                    android:text="测试连接"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/white"/>
            </LinearLayout>

            <!-- 远程路径 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="远程路径："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <Spinner
                    android:id="@+id/sp_remote_path"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1.5"/>

                <Button
                    android:id="@+id/btn_browse"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_weight="0.5"
                    android:text="浏览"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/white"/>
            </LinearLayout>

            <!-- 上传状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="上传状态："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <TextView
                    android:id="@+id/tv_upload_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:text="未连接"
                    android:textSize="20sp"/>
            </LinearLayout>

            <!-- 最后上传时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="最后上传时间："
                android:textSize="22sp"
                    android:minWidth="80dp"/>

                <TextView
                    android:id="@+id/tv_last_upload"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:text="无记录"
                    android:textSize="20sp"/>
            </LinearLayout>
        </LinearLayout>

        <!-- 文件命名规则设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/border_box"
            android:padding="16dp"
            android:layout_marginTop="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="文件命名规则"
                android:textSize="22sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <!-- 视频文件设置 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="?android:attr/selectableItemBackground"
                android:padding="8dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="视频文件"
                android:textSize="22sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"/>

                <CheckBox
                    android:id="@+id/cb_video_time_suffix"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="添加时间后缀"
                    android:layout_marginBottom="8dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="3">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="前缀设置："
                        android:textSize="16sp"/>

                    <EditText
                        android:id="@+id/et_video_prefix"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:inputType="text"
                        android:hint="输入视频前缀"/>
                </LinearLayout>
            </LinearLayout>

            <!-- 图片文件设置 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="?android:attr/selectableItemBackground"
                android:padding="8dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="图片文件"
                android:textSize="22sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"/>

                <CheckBox
                    android:id="@+id/cb_image_time_suffix"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="添加时间后缀"
                    android:layout_marginBottom="8dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="3">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="前缀设置："
                        android:textSize="20sp"/>

                    <EditText
                        android:id="@+id/et_image_prefix"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:inputType="text"
                        android:hint="输入图片前缀"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</ScrollView>