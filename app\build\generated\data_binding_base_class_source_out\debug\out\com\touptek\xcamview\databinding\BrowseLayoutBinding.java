// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BrowseLayoutBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout bottomActionBar;

  @NonNull
  public final ImageView browseConfig;

  @NonNull
  public final TextView browseDelete;

  @NonNull
  public final TextView browseSelectAll;

  @NonNull
  public final TextView btnAction1;

  @NonNull
  public final TextView btnAction2;

  @NonNull
  public final TextView btnAction3;

  @NonNull
  public final TextView btnAction4;

  @NonNull
  public final TextView btnAction5;

  @NonNull
  public final LinearLayout btnContainer1;

  @NonNull
  public final LinearLayout btnContainer2;

  @NonNull
  public final LinearLayout btnContainer3;

  @NonNull
  public final LinearLayout btnContainer4;

  @NonNull
  public final LinearLayout btnContainer5;

  @NonNull
  public final FrameLayout recyclerContainer;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final FrameLayout rightPanel;

  @NonNull
  public final LinearLayout toolbar;

  private BrowseLayoutBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout bottomActionBar, @NonNull ImageView browseConfig,
      @NonNull TextView browseDelete, @NonNull TextView browseSelectAll,
      @NonNull TextView btnAction1, @NonNull TextView btnAction2, @NonNull TextView btnAction3,
      @NonNull TextView btnAction4, @NonNull TextView btnAction5,
      @NonNull LinearLayout btnContainer1, @NonNull LinearLayout btnContainer2,
      @NonNull LinearLayout btnContainer3, @NonNull LinearLayout btnContainer4,
      @NonNull LinearLayout btnContainer5, @NonNull FrameLayout recyclerContainer,
      @NonNull RecyclerView recyclerView, @NonNull FrameLayout rightPanel,
      @NonNull LinearLayout toolbar) {
    this.rootView = rootView;
    this.bottomActionBar = bottomActionBar;
    this.browseConfig = browseConfig;
    this.browseDelete = browseDelete;
    this.browseSelectAll = browseSelectAll;
    this.btnAction1 = btnAction1;
    this.btnAction2 = btnAction2;
    this.btnAction3 = btnAction3;
    this.btnAction4 = btnAction4;
    this.btnAction5 = btnAction5;
    this.btnContainer1 = btnContainer1;
    this.btnContainer2 = btnContainer2;
    this.btnContainer3 = btnContainer3;
    this.btnContainer4 = btnContainer4;
    this.btnContainer5 = btnContainer5;
    this.recyclerContainer = recyclerContainer;
    this.recyclerView = recyclerView;
    this.rightPanel = rightPanel;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BrowseLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BrowseLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.browse_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BrowseLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottom_action_bar;
      LinearLayout bottomActionBar = ViewBindings.findChildViewById(rootView, id);
      if (bottomActionBar == null) {
        break missingId;
      }

      id = R.id.browse_config;
      ImageView browseConfig = ViewBindings.findChildViewById(rootView, id);
      if (browseConfig == null) {
        break missingId;
      }

      id = R.id.browse_delete;
      TextView browseDelete = ViewBindings.findChildViewById(rootView, id);
      if (browseDelete == null) {
        break missingId;
      }

      id = R.id.browse_select_all;
      TextView browseSelectAll = ViewBindings.findChildViewById(rootView, id);
      if (browseSelectAll == null) {
        break missingId;
      }

      id = R.id.btn_action1;
      TextView btnAction1 = ViewBindings.findChildViewById(rootView, id);
      if (btnAction1 == null) {
        break missingId;
      }

      id = R.id.btn_action2;
      TextView btnAction2 = ViewBindings.findChildViewById(rootView, id);
      if (btnAction2 == null) {
        break missingId;
      }

      id = R.id.btn_action3;
      TextView btnAction3 = ViewBindings.findChildViewById(rootView, id);
      if (btnAction3 == null) {
        break missingId;
      }

      id = R.id.btn_action4;
      TextView btnAction4 = ViewBindings.findChildViewById(rootView, id);
      if (btnAction4 == null) {
        break missingId;
      }

      id = R.id.btn_action5;
      TextView btnAction5 = ViewBindings.findChildViewById(rootView, id);
      if (btnAction5 == null) {
        break missingId;
      }

      id = R.id.btn_container1;
      LinearLayout btnContainer1 = ViewBindings.findChildViewById(rootView, id);
      if (btnContainer1 == null) {
        break missingId;
      }

      id = R.id.btn_container2;
      LinearLayout btnContainer2 = ViewBindings.findChildViewById(rootView, id);
      if (btnContainer2 == null) {
        break missingId;
      }

      id = R.id.btn_container3;
      LinearLayout btnContainer3 = ViewBindings.findChildViewById(rootView, id);
      if (btnContainer3 == null) {
        break missingId;
      }

      id = R.id.btn_container4;
      LinearLayout btnContainer4 = ViewBindings.findChildViewById(rootView, id);
      if (btnContainer4 == null) {
        break missingId;
      }

      id = R.id.btn_container5;
      LinearLayout btnContainer5 = ViewBindings.findChildViewById(rootView, id);
      if (btnContainer5 == null) {
        break missingId;
      }

      id = R.id.recycler_container;
      FrameLayout recyclerContainer = ViewBindings.findChildViewById(rootView, id);
      if (recyclerContainer == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.right_panel;
      FrameLayout rightPanel = ViewBindings.findChildViewById(rootView, id);
      if (rightPanel == null) {
        break missingId;
      }

      id = R.id.toolbar;
      LinearLayout toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new BrowseLayoutBinding((ConstraintLayout) rootView, bottomActionBar, browseConfig,
          browseDelete, browseSelectAll, btnAction1, btnAction2, btnAction3, btnAction4, btnAction5,
          btnContainer1, btnContainer2, btnContainer3, btnContainer4, btnContainer5,
          recyclerContainer, recyclerView, rightPanel, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
