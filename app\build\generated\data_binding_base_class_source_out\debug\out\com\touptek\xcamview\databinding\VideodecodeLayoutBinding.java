// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class VideodecodeLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnDecodeReturn;

  @NonNull
  public final Button btnFastBackward;

  @NonNull
  public final Button btnFastForward;

  @NonNull
  public final Button btnPlayPause;

  @NonNull
  public final Button btnStepDecode;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final TextureView surfaceView;

  @NonNull
  public final TextView tvCurrentPosition;

  @NonNull
  public final TextView tvDuration;

  private VideodecodeLayoutBinding(@NonNull LinearLayout rootView, @NonNull Button btnDecodeReturn,
      @NonNull Button btnFastBackward, @NonNull Button btnFastForward, @NonNull Button btnPlayPause,
      @NonNull Button btnStepDecode, @NonNull SeekBar seekBar, @NonNull TextureView surfaceView,
      @NonNull TextView tvCurrentPosition, @NonNull TextView tvDuration) {
    this.rootView = rootView;
    this.btnDecodeReturn = btnDecodeReturn;
    this.btnFastBackward = btnFastBackward;
    this.btnFastForward = btnFastForward;
    this.btnPlayPause = btnPlayPause;
    this.btnStepDecode = btnStepDecode;
    this.seekBar = seekBar;
    this.surfaceView = surfaceView;
    this.tvCurrentPosition = tvCurrentPosition;
    this.tvDuration = tvDuration;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static VideodecodeLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static VideodecodeLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.videodecode_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static VideodecodeLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_decode_return;
      Button btnDecodeReturn = ViewBindings.findChildViewById(rootView, id);
      if (btnDecodeReturn == null) {
        break missingId;
      }

      id = R.id.btn_fast_backward;
      Button btnFastBackward = ViewBindings.findChildViewById(rootView, id);
      if (btnFastBackward == null) {
        break missingId;
      }

      id = R.id.btn_fast_forward;
      Button btnFastForward = ViewBindings.findChildViewById(rootView, id);
      if (btnFastForward == null) {
        break missingId;
      }

      id = R.id.btn_play_pause;
      Button btnPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (btnPlayPause == null) {
        break missingId;
      }

      id = R.id.btn_step_decode;
      Button btnStepDecode = ViewBindings.findChildViewById(rootView, id);
      if (btnStepDecode == null) {
        break missingId;
      }

      id = R.id.seek_bar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.surface_view;
      TextureView surfaceView = ViewBindings.findChildViewById(rootView, id);
      if (surfaceView == null) {
        break missingId;
      }

      id = R.id.tv_current_position;
      TextView tvCurrentPosition = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentPosition == null) {
        break missingId;
      }

      id = R.id.tv_duration;
      TextView tvDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvDuration == null) {
        break missingId;
      }

      return new VideodecodeLayoutBinding((LinearLayout) rootView, btnDecodeReturn, btnFastBackward,
          btnFastForward, btnPlayPause, btnStepDecode, seekBar, surfaceView, tvCurrentPosition,
          tvDuration);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
