package com.touptek.xcamview.activity.browse;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010#\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u00013BW\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\t\u0012\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\t\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000e\u00a2\u0006\u0002\u0010\u000fJ\u0006\u0010\u001f\u001a\u00020\u000bJ\u0006\u0010 \u001a\u00020\u000bJ\u0006\u0010!\u001a\u00020\u000bJ\u000e\u0010\"\u001a\u00020\u00052\u0006\u0010#\u001a\u00020\nJ\u0010\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u0005H\u0002J\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004J\b\u0010\'\u001a\u00020\nH\u0016J\u000e\u0010(\u001a\u00020\u00112\u0006\u0010#\u001a\u00020\nJ\u001c\u0010)\u001a\u00020\u000b2\n\u0010*\u001a\u00060\u0002R\u00020\u00002\u0006\u0010#\u001a\u00020\nH\u0016J\u001c\u0010+\u001a\u00060\u0002R\u00020\u00002\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020\nH\u0016J\u0010\u0010/\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\nH\u0002J\"\u00100\u001a\u00020\u000b2\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004R\u001a\u0010\u0010\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0016\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0013\"\u0004\b\u0017\u0010\u0015R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R(\u0010\u0018\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u001a\"\u0004\b\u001b\u0010\u001cR\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\n0\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpOperationDirAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/touptek/xcamview/activity/browse/TpOperationDirAdapter$ViewHolder;", "imageFiles", "", "Ljava/io/File;", "labels", "", "onClick", "Lkotlin/Function1;", "", "", "onDoubleClick", "onUpdate", "Lkotlin/Function0;", "(Ljava/util/List;Ljava/util/List;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V", "allowSelectionMode", "", "getAllowSelectionMode", "()Z", "setAllowSelectionMode", "(Z)V", "isSelectionMode", "setSelectionMode", "onSelectionChanged", "getOnSelectionChanged", "()Lkotlin/jvm/functions/Function1;", "setOnSelectionChanged", "(Lkotlin/jvm/functions/Function1;)V", "selectedItems", "", "clearData", "enterSelectionMode", "exitSelectionMode", "getFileAt", "position", "getFileType", "file", "getFiles", "getItemCount", "isPositionSelected", "onBindViewHolder", "holder", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "toggleSelection", "updateData", "newFiles", "newLabels", "ViewHolder", "app_debug"})
public final class TpOperationDirAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.touptek.xcamview.activity.browse.TpOperationDirAdapter.ViewHolder> {
    private java.util.List<? extends java.io.File> imageFiles;
    private java.util.List<java.lang.String> labels;
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onClick = null;
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onDoubleClick = null;
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onUpdate = null;
    private final java.util.Set<java.lang.Integer> selectedItems = null;
    private boolean allowSelectionMode = true;
    private boolean isSelectionMode = false;
    @org.jetbrains.annotations.Nullable
    private kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSelectionChanged;
    
    public TpOperationDirAdapter(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.io.File> imageFiles, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> labels, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onDoubleClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onUpdate) {
        super();
    }
    
    public final boolean getAllowSelectionMode() {
        return false;
    }
    
    public final void setAllowSelectionMode(boolean p0) {
    }
    
    public final boolean isSelectionMode() {
        return false;
    }
    
    public final void setSelectionMode(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> getOnSelectionChanged() {
        return null;
    }
    
    public final void setOnSelectionChanged(@org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> p0) {
    }
    
    public final void updateData(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.io.File> newFiles, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> newLabels) {
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public com.touptek.xcamview.activity.browse.TpOperationDirAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.touptek.xcamview.activity.browse.TpOperationDirAdapter.ViewHolder holder, int position) {
    }
    
    public final void enterSelectionMode() {
    }
    
    public final void exitSelectionMode() {
    }
    
    private final void toggleSelection(int position) {
    }
    
    @java.lang.Override
    public int getItemCount() {
        return 0;
    }
    
    public final boolean isPositionSelected(int position) {
        return false;
    }
    
    public final void clearData() {
    }
    
    private final java.lang.String getFileType(java.io.File file) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.io.File> getFiles() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.io.File getFileAt(int position) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\bR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001a\u0010\u000f\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\u001c\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u0011\u0010\u001b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u001e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\b\u00a8\u0006 "}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpOperationDirAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "view", "Landroid/view/View;", "(Lcom/touptek/xcamview/activity/browse/TpOperationDirAdapter;Landroid/view/View;)V", "checkBox", "Landroid/widget/ImageView;", "getCheckBox", "()Landroid/widget/ImageView;", "imageView", "getImageView", "labelView", "Landroid/widget/TextView;", "getLabelView", "()Landroid/widget/TextView;", "lastClickTime", "", "getLastClickTime", "()J", "setLastClickTime", "(J)V", "pendingClickRunnable", "Ljava/lang/Runnable;", "getPendingClickRunnable", "()Ljava/lang/Runnable;", "setPendingClickRunnable", "(Ljava/lang/Runnable;)V", "selectedOverlay", "getSelectedOverlay", "()Landroid/view/View;", "videoIndicator", "getVideoIndicator", "app_debug"})
    public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final android.widget.ImageView imageView = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView labelView = null;
        @org.jetbrains.annotations.NotNull
        private final android.view.View selectedOverlay = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.ImageView videoIndicator = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.ImageView checkBox = null;
        private long lastClickTime = 0L;
        @org.jetbrains.annotations.Nullable
        private java.lang.Runnable pendingClickRunnable;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull
        android.view.View view) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.ImageView getImageView() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getLabelView() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.view.View getSelectedOverlay() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.ImageView getVideoIndicator() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.ImageView getCheckBox() {
            return null;
        }
        
        public final long getLastClickTime() {
            return 0L;
        }
        
        public final void setLastClickTime(long p0) {
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.Runnable getPendingClickRunnable() {
            return null;
        }
        
        public final void setPendingClickRunnable(@org.jetbrains.annotations.Nullable
        java.lang.Runnable p0) {
        }
    }
}