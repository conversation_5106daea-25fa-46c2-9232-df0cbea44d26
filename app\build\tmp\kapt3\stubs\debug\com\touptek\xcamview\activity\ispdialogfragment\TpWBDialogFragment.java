package com.touptek.xcamview.activity.ispdialogfragment;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0018\u0010\u000f\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0002J\u0018\u0010\u0013\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0002J\b\u0010\u0014\u001a\u00020\tH\u0014J\u0010\u0010\u0015\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010\u0017\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010\u0018\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010\u0019\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\b\u0010\u001a\u001a\u00020\fH\u0002J&\u0010\u001b\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001f2\b\u0010 \u001a\u0004\u0018\u00010!H\u0016J\b\u0010\"\u001a\u00020\fH\u0016J\u0010\u0010#\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J(\u0010$\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010%\u001a\u00020\t2\u0006\u0010&\u001a\u00020\'H\u0002J\u0018\u0010(\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010)\u001a\u00020\tH\u0002J0\u0010*\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010+\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010,\u001a\u00020\t2\u0006\u0010-\u001a\u00020\tH\u0002J \u0010.\u001a\u00020\f2\u0006\u0010/\u001a\u0002002\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010)\u001a\u00020\tH\u0002J(\u00101\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010%\u001a\u00020\t2\u0006\u0010&\u001a\u00020\'H\u0002J\u0010\u00102\u001a\u00020\f2\u0006\u00103\u001a\u000204H\u0002J\u0018\u00105\u001a\u00020\f2\u0006\u0010&\u001a\u00020\'2\u0006\u00106\u001a\u00020\tH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00067"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/TpWBDialogFragment;", "Lcom/touptek/xcamview/activity/ispdialogfragment/BaseISPDialogFragment;", "()V", "handler", "Landroid/os/Handler;", "radioGroup", "Landroid/widget/RadioGroup;", "seekBarListeners", "", "", "Landroid/widget/SeekBar$OnSeekBarChangeListener;", "changeAdjustBtnState", "", "enable", "", "disableSeekBar", "view", "Landroid/view/View;", "seekBarId", "enableSeekBar", "getDialogHeight", "handleWBDefaultButtonClick", "initButtonControls", "initParameterValue", "initRadioGroup", "initSeekbar", "initTpIspParam", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onStart", "setDefaultParameter", "setSeekBarProgress", "textViewId", "param", "Lcom/touptek/video/TpIspParam;", "setShortPress", "delta", "setupButtonControl", "buttonId", "shortIncrement", "longIncrement", "setupLongPress", "button", "Landroid/widget/ImageButton;", "setupSeekBar", "showToast", "message", "", "updateSeekBar", "newValue", "app_debug"})
public final class TpWBDialogFragment extends com.touptek.xcamview.activity.ispdialogfragment.BaseISPDialogFragment {
    private final android.os.Handler handler = null;
    private final java.util.Map<java.lang.Integer, android.widget.SeekBar.OnSeekBarChangeListener> seekBarListeners = null;
    private android.widget.RadioGroup radioGroup;
    
    public TpWBDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    @java.lang.Override
    protected int getDialogHeight() {
        return 0;
    }
    
    private final void initTpIspParam() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void initParameterValue(android.view.View view) {
    }
    
    private final void initSeekbar(android.view.View view) {
    }
    
    private final void initRadioGroup(android.view.View view) {
    }
    
    private final void setupSeekBar(android.view.View view, int seekBarId, int textViewId, com.touptek.video.TpIspParam param) {
    }
    
    private final void disableSeekBar(android.view.View view, int seekBarId) {
    }
    
    private final void enableSeekBar(android.view.View view, int seekBarId) {
    }
    
    private final void setSeekBarProgress(android.view.View view, int seekBarId, int textViewId, com.touptek.video.TpIspParam param) {
    }
    
    private final void updateSeekBar(com.touptek.video.TpIspParam param, int newValue) {
    }
    
    private final void handleWBDefaultButtonClick(android.view.View view) {
    }
    
    private final void initButtonControls(android.view.View view) {
    }
    
    private final void setupButtonControl(android.view.View view, int buttonId, int seekBarId, int shortIncrement, int longIncrement) {
    }
    
    private final void setShortPress(int seekBarId, int delta) {
    }
    
    private final void setupLongPress(android.widget.ImageButton button, int seekBarId, int delta) {
    }
    
    private final void changeAdjustBtnState(boolean enable) {
    }
    
    private final void setDefaultParameter(android.view.View view) {
    }
}