{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\1z4oy32e\\arm64-v8a", "soFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\1z4oy32e\\obj\\arm64-v8a", "soRepublishFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 31, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": ["-std=c++11"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\.cxx", "intermediatesBaseFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates", "intermediatesFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "E:\\rk\\AndroidStudio\\XCamView\\app", "moduleBuildFile": "E:\\rk\\AndroidStudio\\XCamView\\app\\build.gradle.kts", "makeFile": "E:\\rk\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125", "ndkFolderBeforeSymLinking": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "E:\\androidsdk\\sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "E:\\androidsdk\\sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "E:\\rk\\AndroidStudio\\XCamView", "sdkFolder": "E:\\androidsdk\\sdk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "arm64-v8a,armeabi-v7a,armeabi", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "E:\\androidsdk\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\1z4oy32e\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "1z4oy32e4l4a4t1f4f7633g3y5lyo3y6g4m6t1o6m4a4t1p193u4m6f5d6a", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.6.0.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJ<PERSON> is the path to Ninja.\n-H$PROJECT/app/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=31\n-DANDROID_PLATFORM=android-31\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-std=c++11\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/app/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HE:\\rk\\AndroidStudio\\XCamView\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=31", "-DANDROID_PLATFORM=android-31", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=E:\\androidsdk\\sdk\\ndk\\26.1.10909125", "-DCMAKE_ANDROID_NDK=E:\\androidsdk\\sdk\\ndk\\26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=E:\\androidsdk\\sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=E:\\androidsdk\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-std=c++11", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\1z4oy32e\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\1z4oy32e\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BE:\\rk\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\1z4oy32e\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "E:\\rk\\AndroidStudio\\XCamView\\app\\build\\intermediates\\cxx\\Debug\\1z4oy32e"}