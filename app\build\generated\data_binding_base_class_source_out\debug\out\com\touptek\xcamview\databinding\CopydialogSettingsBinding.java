// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CopydialogSettingsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageButton btnClose;

  @NonNull
  public final Button btnConfirm;

  @NonNull
  public final RecyclerView rvFolders;

  @NonNull
  public final TextView tvCurrentPath;

  @NonNull
  public final TextView tvTitle;

  private CopydialogSettingsBinding(@NonNull FrameLayout rootView, @NonNull ImageButton btnClose,
      @NonNull Button btnConfirm, @NonNull RecyclerView rvFolders, @NonNull TextView tvCurrentPath,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.btnConfirm = btnConfirm;
    this.rvFolders = rvFolders;
    this.tvCurrentPath = tvCurrentPath;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CopydialogSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CopydialogSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.copydialog_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CopydialogSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_close;
      ImageButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.btn_confirm;
      Button btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.rv_folders;
      RecyclerView rvFolders = ViewBindings.findChildViewById(rootView, id);
      if (rvFolders == null) {
        break missingId;
      }

      id = R.id.tv_current_path;
      TextView tvCurrentPath = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentPath == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new CopydialogSettingsBinding((FrameLayout) rootView, btnClose, btnConfirm, rvFolders,
          tvCurrentPath, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
