{"logs": [{"outputFile": "com.touptek.xcamview.app-mergeDebugResources-39:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\560db8d7606c3580fcf519784f6f6a65\\transformed\\appcompat-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,8581", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,8660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be4ee3b3d380c351331d5de220cf8d41\\transformed\\core-1.9.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8665", "endColumns": "100", "endOffsets": "8761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2052e42c61716abd4b6d39c7be558473\\transformed\\material-1.10.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1033,1132,1207,1266,1376,1438,1507,1565,1637,1698,1753,1856,1913,1973,2028,2109,2229,2312,2400,2535,2618,2698,2838,2932,3014,3067,3118,3184,3260,3342,3428,3512,3589,3664,3743,3820,3925,4021,4098,4190,4287,4361,4446,4543,4595,4678,4745,4833,4920,4982,5046,5109,5175,5273,5379,5473,5580,5637,5692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "258,339,415,492,582,662,761,881,964,1028,1127,1202,1261,1371,1433,1502,1560,1632,1693,1748,1851,1908,1968,2023,2104,2224,2307,2395,2530,2613,2693,2833,2927,3009,3062,3113,3179,3255,3337,3423,3507,3584,3659,3738,3815,3920,4016,4093,4185,4282,4356,4441,4538,4590,4673,4740,4828,4915,4977,5041,5104,5170,5268,5374,5468,5575,5632,5687,5772"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3067,3148,3224,3301,3391,3471,3570,3690,3773,3837,3936,4011,4070,4180,4242,4311,4369,4441,4502,4557,4660,4717,4777,4832,4913,5033,5116,5204,5339,5422,5502,5642,5736,5818,5871,5922,5988,6064,6146,6232,6316,6393,6468,6547,6624,6729,6825,6902,6994,7091,7165,7250,7347,7399,7482,7549,7637,7724,7786,7850,7913,7979,8077,8183,8277,8384,8441,8496", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "308,3143,3219,3296,3386,3466,3565,3685,3768,3832,3931,4006,4065,4175,4237,4306,4364,4436,4497,4552,4655,4712,4772,4827,4908,5028,5111,5199,5334,5417,5497,5637,5731,5813,5866,5917,5983,6059,6141,6227,6311,6388,6463,6542,6619,6724,6820,6897,6989,7086,7160,7245,7342,7394,7477,7544,7632,7719,7781,7845,7908,7974,8072,8178,8272,8379,8436,8491,8576"}}]}]}