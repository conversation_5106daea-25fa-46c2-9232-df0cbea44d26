package com.touptek.xcamview.activity.ispdialogfragment

import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.WindowManager
import com.touptek.xcamview.util.BaseDialogFragment

/**
 * ISP对话框基类，提供统一的宽度和位置计算逻辑
 * 对话框宽度：360dp（覆盖4个按钮：btn_exposure → btn_image_processing）
 * 水平对齐：左边缘与btn_exposure对齐，右边缘与btn_image_processing对齐
 * 垂直对齐：底部边缘与TpFlipDialogFragment的底部边缘对齐
 */
abstract class BaseISPDialogFragment : BaseDialogFragment() {
    
    companion object {
        private const val TAG = "BaseISPDialogFragment"

        // 4个按钮组的总宽度计算（第2-5个按钮）：
        // 4个按钮 × 70dp + 3个间距 × 16dp = 280dp + 48dp = 328dp
        // 加上容器的左右padding：16dp × 2 = 32dp
        // 总宽度：328dp + 32dp = 360dp
        private const val BUTTON_GROUP_WIDTH_DP = 360

        // 对话框与按钮组的垂直间距
        private const val VERTICAL_MARGIN_DP = 20

        // TpFlipDialogFragment的基准位置计算
        // TpFlipDialogFragment使用：params.y = buttonY - 45，预估高度约120dp
        // 所以其底部位置 = buttonY - 45 + 120 = buttonY + 75
        private const val FLIP_DIALOG_BOTTOM_OFFSET_DP = 75
    }

    override fun onStart() {
        super.onStart()
        setupDialogWindow()
    }
    
    private fun setupDialogWindow() {
        val dialog = dialog ?: return
        val window = dialog.window ?: return
        
        // 设置透明背景
        window.setDimAmount(0f)
        window.setBackgroundDrawableResource(android.R.color.transparent)
        
        val params = window.attributes
        val displayMetrics = resources.displayMetrics
        
        // 计算对话框宽度（与按钮组宽度一致）
        val dialogWidthPx = (BUTTON_GROUP_WIDTH_DP * displayMetrics.density).toInt()
        
        // 获取传递的位置参数
        val args = arguments
        if (args != null) {
            val buttonGroupX = args.getInt("button_group_x", 0)
            val buttonGroupY = args.getInt("button_group_y", 0)
            val buttonGroupWidth = args.getInt("button_group_width", 0)

            // 计算对话框位置
            val position = calculateDialogPosition(
                buttonGroupX, buttonGroupY, buttonGroupWidth,
                dialogWidthPx, displayMetrics
            )

            params.x = position.first
            params.y = position.second

            Log.d(TAG, "对话框位置计算: 4个按钮组($buttonGroupX, $buttonGroupY, ${buttonGroupWidth}px), " +
                    "对话框宽度(${dialogWidthPx}px), 最终位置(${params.x}, ${params.y}), " +
                    "底部对齐到TpFlipDialogFragment")
        }
        
        // 设置对话框尺寸
        params.width = dialogWidthPx
        params.height = WindowManager.LayoutParams.WRAP_CONTENT
        params.gravity = Gravity.TOP or Gravity.START
        
        window.attributes = params
    }
    
    private fun calculateDialogPosition(
        buttonGroupX: Int,
        buttonGroupY: Int, 
        buttonGroupWidth: Int,
        dialogWidthPx: Int,
        displayMetrics: android.util.DisplayMetrics
    ): Pair<Int, Int> {
        
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        val verticalMarginPx = (VERTICAL_MARGIN_DP * displayMetrics.density).toInt()
        val flipBottomOffsetPx = (FLIP_DIALOG_BOTTOM_OFFSET_DP * displayMetrics.density).toInt()

        // X轴对齐：对话框左边缘与第二个按钮（btn_exposure）左边缘对齐
        var dialogX = buttonGroupX

        // Y轴位置：底部对齐到TpFlipDialogFragment的底部位置
        // TpFlipDialogFragment的底部位置 = buttonY + FLIP_DIALOG_BOTTOM_OFFSET_DP
        // 当前对话框的顶部位置 = TpFlipDialogFragment底部位置 - 当前对话框高度
        val flipDialogBottomY = buttonGroupY + flipBottomOffsetPx
        var dialogY = flipDialogBottomY - getDialogHeight()
        
        // 边界检查和调整
        
        // X轴边界检查
        if (dialogX < 20) {
            dialogX = 20 // 左边界最小间距
        } else if (dialogX + dialogWidthPx > screenWidth - 20) {
            dialogX = screenWidth - dialogWidthPx - 20 // 右边界最小间距
        }
        
        // Y轴边界检查
        if (dialogY < 20) {
            // 上方空间不足，显示在按钮组下方，但仍保持底部对齐
            dialogY = buttonGroupY + 70 + verticalMarginPx // 70dp是按钮高度

            // 如果下方也不足，尽量显示在屏幕内
            if (dialogY + getDialogHeight() > screenHeight - 20) {
                dialogY = screenHeight - getDialogHeight() - 20
            }
        }
        
        // 最终边界保护
        dialogX = dialogX.coerceIn(20, screenWidth - dialogWidthPx - 20)
        dialogY = dialogY.coerceIn(20, screenHeight - getDialogHeight() - 20)
        
        return Pair(dialogX, dialogY)
    }
    
    /**
     * 子类需要实现此方法，返回对话框的预估高度（像素）
     * 用于位置计算时的边界检查
     */
    protected abstract fun getDialogHeight(): Int
    
    /**
     * 获取标准的对话框高度（dp转像素）
     */
    protected fun dpToPx(dp: Int): Int {
        return (dp * resources.displayMetrics.density).toInt()
    }
}
