package com.touptek.ui.compare;

import java.lang.System;

/**
 * 三图对比Activity - 支持2+1布局的三张图片对比
 * 支持全局同步、分组同步、独立操作三种模式
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 (2\u00020\u0001:\u0001(B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0017\u001a\u00020\u0018H\u0002J\u0010\u0010\u0019\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\fH\u0002J\b\u0010\u001b\u001a\u00020\u0018H\u0002J\b\u0010\u001c\u001a\u00020\u0018H\u0002J\b\u0010\u001d\u001a\u00020\u0018H\u0002J\u0012\u0010\u001e\u001a\u00020\u00182\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0014J\b\u0010!\u001a\u00020\u0018H\u0014J\b\u0010\"\u001a\u00020\u0018H\u0002J\b\u0010#\u001a\u00020\u0018H\u0002J\b\u0010$\u001a\u00020\u0018H\u0002J\b\u0010%\u001a\u00020\u0018H\u0002J\b\u0010&\u001a\u00020\u0018H\u0002J\b\u0010\'\u001a\u00020\u0018H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/touptek/ui/compare/TpImageCompareTripleActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnBack", "Landroid/widget/ImageButton;", "btnReset", "btnSwapPosition", "btnSyncMode", "currentSyncMode", "Lcom/touptek/ui/compare/TpImageSyncEngine$SyncMode;", "imagePaths", "", "", "imageViewCenter", "Lcom/touptek/ui/TpImageView;", "imageViewLeft", "imageViewRight", "syncEngine", "Lcom/touptek/ui/compare/TpImageSyncEngine;", "tvInfoCenter", "Landroid/widget/TextView;", "tvInfoLeft", "tvInfoRight", "cycleSyncMode", "", "getImageResolution", "imagePath", "getIntentData", "initViews", "loadImages", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "setupClickListeners", "setupFullScreen", "setupSyncEngine", "swapImages", "updateImageInfo", "updateSyncModeButtonState", "Companion", "app_debug"})
public final class TpImageCompareTripleActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.ui.compare.TpImageCompareTripleActivity.Companion Companion = null;
    private static final java.lang.String TAG = "TpImageCompareTriple";
    private static final java.lang.String EXTRA_IMAGE_PATHS = "extra_image_paths";
    private android.widget.ImageButton btnBack;
    private android.widget.ImageButton btnSyncMode;
    private android.widget.ImageButton btnReset;
    private android.widget.ImageButton btnSwapPosition;
    private com.touptek.ui.TpImageView imageViewLeft;
    private com.touptek.ui.TpImageView imageViewCenter;
    private com.touptek.ui.TpImageView imageViewRight;
    private android.widget.TextView tvInfoLeft;
    private android.widget.TextView tvInfoCenter;
    private android.widget.TextView tvInfoRight;
    private java.util.List<java.lang.String> imagePaths;
    private com.touptek.ui.compare.TpImageSyncEngine.SyncMode currentSyncMode = com.touptek.ui.compare.TpImageSyncEngine.SyncMode.GLOBAL;
    private com.touptek.ui.compare.TpImageSyncEngine syncEngine;
    
    public TpImageCompareTripleActivity() {
        super();
    }
    
    /**
     * 启动三图对比Activity
     */
    @kotlin.jvm.JvmStatic
    public static final void start(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> imagePaths) {
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupFullScreen() {
    }
    
    private final void initViews() {
    }
    
    private final void getIntentData() {
    }
    
    private final void setupSyncEngine() {
    }
    
    private final void loadImages() {
    }
    
    private final void updateImageInfo() {
    }
    
    private final java.lang.String getImageResolution(java.lang.String imagePath) {
        return null;
    }
    
    private final void setupClickListeners() {
    }
    
    private final void cycleSyncMode() {
    }
    
    private final void updateSyncModeButtonState() {
    }
    
    private final void swapImages() {
    }
    
    @java.lang.Override
    protected void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\u000bH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/ui/compare/TpImageCompareTripleActivity$Companion;", "", "()V", "EXTRA_IMAGE_PATHS", "", "TAG", "start", "", "context", "Landroid/content/Context;", "imagePaths", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 启动三图对比Activity
         */
        @kotlin.jvm.JvmStatic
        public final void start(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> imagePaths) {
        }
    }
}