package com.android.rockchip.camera2.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View

class OverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : View(context, attrs, defStyle) {

    private val paint = Paint().apply {
        color = Color.YELLOW
        style = Paint.Style.STROKE
        strokeWidth = 5f
        isAntiAlias = true
    }

    private var startX = 0f
    private var startY = 0f
    private var endX = 0f
    private var endY = 0f
    private var isDrawing = false

    fun resetDrawing() {
        startX = 0f
        startY = 0f
        endX = 0f
        endY = 0f
        isDrawing = false
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (isDrawing) {
            canvas.drawRect(startX, startY, endX, endY, paint)
        }
    }

    fun setStartPoint(x: Float, y: Float) {
        startX = x
        startY = y
    }

    fun setEndPoint(x: Float, y: Float) {
        endX = x
        endY = y
        invalidate()
    }

    fun beginDrawing() {
        isDrawing = true
        invalidate()
    }
}