<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="RoundelMenu">
        <attr name="round_menu_roundColor" format="color|reference" />
        <attr name="round_menu_centerColor" format="color|reference" />
        <attr name="round_menu_expandedRadius" format="dimension" />
        <attr name="round_menu_collapsedRadius" format="dimension" />
        <attr name="round_menu_duration" format="integer" />
        <attr name="round_menu_item_anim_delay" format="integer" />
        <attr name="round_menu_item_width" format="dimension" />
    </declare-styleable>
</resources>
