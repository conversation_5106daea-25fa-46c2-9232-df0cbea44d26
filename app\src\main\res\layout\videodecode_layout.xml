<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:orientation="vertical">

    <TextureView
        android:id="@+id/surface_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <SeekBar
        android:id="@+id/seek_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            android:id="@+id/tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="总时长: 00:00:00" />

        <TextView
            android:id="@+id/tv_current_position"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前时长: 00:00:00" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_play_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="播放" />

        <Button
            android:id="@+id/btn_fast_forward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="快进" />

        <Button
            android:id="@+id/btn_fast_backward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="快退" />

        <Button
            android:id="@+id/btn_step_decode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="逐帧播放" />

        <Button
            android:id="@+id/btn_decode_return"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="返回" />
    </LinearLayout>
</LinearLayout>