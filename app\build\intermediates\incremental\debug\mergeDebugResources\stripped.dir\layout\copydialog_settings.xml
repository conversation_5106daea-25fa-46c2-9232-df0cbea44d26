<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/white_background"
        android:padding="8dp">

        <!-- 顶部标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="8dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选择目标文件夹"
                android:textSize="18sp"
                android:textColor="@color/black"
                android:layout_centerHorizontal="true"/>

            <ImageButton
                android:id="@+id/btn_close"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_close"
                android:background="?attr/selectableItemBackgroundBorderless"/>
        </RelativeLayout>

        <!-- 当前路径显示 -->
        <TextView
            android:id="@+id/tv_current_path"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/gray_text"
            android:padding="8dp"
            android:ellipsize="end"
            android:maxLines="1"/>

        <!-- 文件夹列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_folders"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scrollbars="vertical"
            android:padding="8dp"/>
        <!-- 底部按钮栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp"
            android:gravity="end">

            <Button
                android:id="@+id/btn_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="确认"
                android:minWidth="120dp"
                android:background="@color/grey_background"
                android:textColor="@color/white"
                android:textSize="16sp"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>