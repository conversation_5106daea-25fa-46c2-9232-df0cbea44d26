<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 适用于Android 11及以上 -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="XCamView"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar"
        tools:targetApi="31"
        tools:replace="android:allowBackup,android:label">

<!--        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar"-->
<!--        android:theme="@style/Theme.AppCompat.DayNight"-->

        <!-- 设置MainActivity为启动Activity -->
        <activity
            android:name="com.touptek.xcamview.activity.MainActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:screenOrientation="unspecified"
            android:supportsPictureInPicture="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".activity.videomanagement.TpVideoEncoderActivity" />
        <activity android:name=".activity.videomanagement.TpVideoDecoderActivity" />
        <activity android:name=".activity.browse.TpVideoBrowse" />

    </application>

</manifest>
