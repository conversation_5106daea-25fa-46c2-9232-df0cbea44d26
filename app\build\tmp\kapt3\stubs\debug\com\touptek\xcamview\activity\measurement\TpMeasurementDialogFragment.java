package com.touptek.xcamview.activity.measurement;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\n\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\u000b\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\f\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\r\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\u000e\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\u000f\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\u0010\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\u0011\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\u0012\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J&\u0010\u0013\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0016J\b\u0010\u001b\u001a\u00020\u0007H\u0016J\u0010\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u0014H\u0002J\u0010\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020 H\u0002J\u0010\u0010!\u001a\u00020\u00072\u0006\u0010\"\u001a\u00020\u0005H\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/touptek/xcamview/activity/measurement/TpMeasurementDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "buttons", "", "Landroid/widget/ImageButton;", "handleAngleClick", "", "isSelected", "", "handleAngleThreeClick", "handleArbClick", "handleHorizonLineClick", "handleParallelClick", "handlePointClick", "handleRectangleClick", "handleThreeLineClick", "handleThreeVerticalClick", "handleVerticalLineClick", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onStart", "setupButtons", "view", "showToast", "message", "", "toggleButtonState", "selectedButton", "app_debug"})
public final class TpMeasurementDialogFragment extends androidx.fragment.app.DialogFragment {
    private final java.util.List<android.widget.ImageButton> buttons = null;
    
    public TpMeasurementDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void setupButtons(android.view.View view) {
    }
    
    private final void toggleButtonState(android.widget.ImageButton selectedButton) {
    }
    
    private final void handleAngleClick(boolean isSelected) {
    }
    
    private final void handleAngleThreeClick(boolean isSelected) {
    }
    
    private final void handlePointClick(boolean isSelected) {
    }
    
    private final void handleArbClick(boolean isSelected) {
    }
    
    private final void handleThreeLineClick(boolean isSelected) {
    }
    
    private final void handleHorizonLineClick(boolean isSelected) {
    }
    
    private final void handleVerticalLineClick(boolean isSelected) {
    }
    
    private final void handleParallelClick(boolean isSelected) {
    }
    
    private final void handleThreeVerticalClick(boolean isSelected) {
    }
    
    private final void handleRectangleClick(boolean isSelected) {
    }
    
    private final void showToast(java.lang.String message) {
    }
}