import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.touptek.xcamview.R
import java.io.File

class FolderAdapter(
    private var folders: List<File>,
    private val onFolderSelected: (File?) -> Unit  // 修改为可接受null表示返回上一级
) : RecyclerView.Adapter<FolderAdapter.ViewHolder>() {

    private var showBackToParent = false
    // 添加父目录引用
    private var parentFolder: File? = null
    // 添加根目录标记
    private var isRootFolder = true

    // 设置父目录并更新数据
    fun updateFolders(newFolders: List<File>, parent: File?) {
        parentFolder = parent
        folders = newFolders
        // 如果父目录非空，则不是根目录
        isRootFolder = (parent == null)
        notifyDataSetChanged()
    }

    fun getFolders() = folders

    fun setShowBackToParent(show: Boolean, parent: File?) {
        this.showBackToParent = show
        this.parentFolder = parent
        notifyDataSetChanged()
    }


    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val folderName: TextView = itemView.findViewById(R.id.tv_folder_name)
        val fileCount: TextView = itemView.findViewById(R.id.tv_file_count)

        init {
            itemView.setOnClickListener {
                val position = adapterPosition
                if (position == 0 && parentFolder != null) {
                    // 点击返回上一级项
                    onFolderSelected(parentFolder)
                } else {
                    // 计算实际文件夹位置（考虑返回上一级项）
                    val actualPosition = if (parentFolder != null) position - 1 else position
                    if (actualPosition in 0 until folders.size) {
                        onFolderSelected(folders[actualPosition])
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.folder_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (!isRootFolder && position == 0) {
            // 第一项显示返回上一级
            holder.folderName.text = ".. (返回上一级)"
            holder.fileCount.text = ""
        } else {
            // 计算实际文件夹位置（考虑返回上一级项）
            val actualPosition = if (!isRootFolder) position - 1 else position
            val folder = folders[actualPosition]
            holder.folderName.text = folder.name

            // 计算文件夹中的项目数（包括文件和子文件夹）
            val itemCount = folder.listFiles()?.size ?: 0
            holder.fileCount.text = "$itemCount 项"
        }
    }

    override fun getItemCount(): Int {
        // 如果有父目录，添加一个返回上一级项
        return folders.size + if (parentFolder != null) 1 else 0
    }
}