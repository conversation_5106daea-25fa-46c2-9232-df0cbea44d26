// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SceneLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAddScene;

  @NonNull
  public final Button btnDeleteScene;

  @NonNull
  public final Spinner sceneSpinner;

  private SceneLayoutBinding(@NonNull LinearLayout rootView, @NonNull Button btnAddScene,
      @NonNull Button btnDeleteScene, @NonNull Spinner sceneSpinner) {
    this.rootView = rootView;
    this.btnAddScene = btnAddScene;
    this.btnDeleteScene = btnDeleteScene;
    this.sceneSpinner = sceneSpinner;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SceneLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SceneLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.scene_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SceneLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_scene;
      Button btnAddScene = ViewBindings.findChildViewById(rootView, id);
      if (btnAddScene == null) {
        break missingId;
      }

      id = R.id.btn_delete_scene;
      Button btnDeleteScene = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteScene == null) {
        break missingId;
      }

      id = R.id.scene_spinner;
      Spinner sceneSpinner = ViewBindings.findChildViewById(rootView, id);
      if (sceneSpinner == null) {
        break missingId;
      }

      return new SceneLayoutBinding((LinearLayout) rootView, btnAddScene, btnDeleteScene,
          sceneSpinner);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
