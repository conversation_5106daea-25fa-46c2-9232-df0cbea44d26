<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/rounded_border"
    android:layout_margin="8dp">

    <RadioGroup
        android:id="@+id/hz_btn_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:divider="@null">

        <!-- 第一个RadioButton -->
        <RadioButton
            android:id="@+id/radio_ac_50hz_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:button="@drawable/tp_custom_radionbutton"
            android:text="AC(50Hz)"
            android:textColor="@color/colorISPText"/>

        <!-- 第二个RadioButton -->
        <RadioButton
            android:id="@+id/radio_ac_60hz_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:button="@drawable/tp_custom_radionbutton"
            android:text="AC(60Hz)"
            android:textColor="@color/colorISPText"/>

        <!-- 第三个RadioButton -->
        <RadioButton
            android:id="@+id/radio_dc_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:button="@drawable/tp_custom_radionbutton"
            android:text="DC"
            android:textColor="@color/colorISPText"/>

    </RadioGroup>

</LinearLayout>