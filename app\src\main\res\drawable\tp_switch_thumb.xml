<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 开启状态 -->
    <item android:state_checked="true">
        <shape android:shape="oval">
            <solid android:color="#FFFFFF"/> <!-- 滑块开启时颜色（白色） -->
            <stroke
                android:width="2dp"
                android:color="#1976D2"/> <!-- 边框颜色（深蓝） -->
        </shape>
    </item>

    <!-- 关闭状态 -->
    <item android:state_checked="false">
        <shape android:shape="oval">
            <solid android:color="#FFFFFF"/> <!-- 滑块关闭时颜色（白色） -->
            <stroke
                android:width="2dp"
                android:color="#BDBDBD"/> <!-- 边框颜色（灰） -->
        </shape>
    </item>
</selector>