<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@drawable/rounded_border"
    android:gravity="center_vertical">

    <!-- 场景选择标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="场景管理"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"
        android:textColor="#333333"
        android:gravity="center"
        android:background="@drawable/title_background"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"/>

    <!-- 场景选择器 -->
    <Spinner
        android:id="@+id/scene_spinner"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/spinner_background"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"/>

    <!-- 按钮容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="8dp">

        <!-- 添加场景按钮 -->
        <Button
            android:id="@+id/btn_add_scene"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginEnd="10dp"
            android:text="添加"
            android:textSize="15sp"
            android:background="#808080"
            android:textColor="#000000"/>

        <!-- 删除场景按钮 -->
        <Button
            android:id="@+id/btn_delete_scene"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:text="删除"
            android:textSize="15sp"
            android:background="#808080"
            android:textColor="#000000"/>

    </LinearLayout>

</LinearLayout>