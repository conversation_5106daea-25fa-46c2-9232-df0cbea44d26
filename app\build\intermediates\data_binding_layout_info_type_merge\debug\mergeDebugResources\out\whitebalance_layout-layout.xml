<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="whitebalance_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\whitebalance_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/whitebalance_layout_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="257" endOffset="14"/></Target><Target id="@+id/wb_btn_group" view="RadioGroup"><Expressions/><location startLine="16" startOffset="8" endLine="45" endOffset="20"/></Target><Target id="@+id/radio_auto_tv" view="RadioButton"><Expressions/><location startLine="22" startOffset="12" endLine="28" endOffset="57"/></Target><Target id="@+id/radio_manual_tv" view="RadioButton"><Expressions/><location startLine="30" startOffset="12" endLine="36" endOffset="57"/></Target><Target id="@+id/radio_roi_tv" view="RadioButton"><Expressions/><location startLine="38" startOffset="12" endLine="44" endOffset="57"/></Target><Target id="@+id/text_red_value" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="76" endOffset="41"/></Target><Target id="@+id/btn_red_reduce" view="ImageButton"><Expressions/><location startLine="86" startOffset="12" endLine="92" endOffset="47"/></Target><Target id="@+id/seekbar_red_tv" view="SeekBar"><Expressions/><location startLine="94" startOffset="12" endLine="101" endOffset="57"/></Target><Target id="@+id/btn_red_add" view="ImageButton"><Expressions/><location startLine="103" startOffset="12" endLine="109" endOffset="47"/></Target><Target id="@+id/text_green_value" view="TextView"><Expressions/><location startLine="132" startOffset="12" endLine="140" endOffset="41"/></Target><Target id="@+id/btn_green_reduce" view="ImageButton"><Expressions/><location startLine="150" startOffset="12" endLine="156" endOffset="47"/></Target><Target id="@+id/seekbar_green_tv" view="SeekBar"><Expressions/><location startLine="158" startOffset="12" endLine="165" endOffset="57"/></Target><Target id="@+id/btn_green_add" view="ImageButton"><Expressions/><location startLine="167" startOffset="12" endLine="173" endOffset="47"/></Target><Target id="@+id/text_blue_value" view="TextView"><Expressions/><location startLine="196" startOffset="12" endLine="204" endOffset="41"/></Target><Target id="@+id/btn_blue_reduce" view="ImageButton"><Expressions/><location startLine="214" startOffset="12" endLine="220" endOffset="47"/></Target><Target id="@+id/seekbar_blue_tv" view="SeekBar"><Expressions/><location startLine="222" startOffset="12" endLine="229" endOffset="57"/></Target><Target id="@+id/btn_blue_add" view="ImageButton"><Expressions/><location startLine="231" startOffset="12" endLine="237" endOffset="47"/></Target><Target id="@+id/btn_Default_wb" view="Button"><Expressions/><location startLine="248" startOffset="8" endLine="253" endOffset="64"/></Target></Targets></Layout>