<!-- res/layout/dialog_settings.xml -->
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:divider="@drawable/grey_background"
        android:showDividers="middle"
        android:background="@color/white_background">

        <!-- 左侧导航栏 -->
        <LinearLayout
            android:id="@+id/navigation_container"
            android:layout_width="160dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/white_background">

            <TextView
                android:id="@+id/item_format"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:gravity="center"
                android:text="图像格式"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/item_video"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:gravity="center"
                android:text="录像"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/item_misc"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:gravity="center"
                android:text="杂项"
                android:textSize="16sp" />
        </LinearLayout>

        <!-- 右侧内容容器 -->
        <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white_background"/>
    </LinearLayout>

    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="top|end"
        android:layout_margin="8dp"
        android:src="@drawable/ic_close"
        android:background="?attr/selectableItemBackgroundBorderless"/>
</FrameLayout>