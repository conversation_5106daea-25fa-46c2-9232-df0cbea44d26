# 字体设置功能修复测试指南

## 修复内容概述

### 问题描述
原先在设置界面的"杂项设置"中选择不同字体时，只有Toast提示消息的字体发生改变，设置界面本身的UI文字字体没有更新。

### 修复方案
1. **扩展FontUtils功能**：添加了对DialogFragment的支持
2. **创建BaseDialogFragment基类**：自动处理字体注册和应用
3. **修改设置相关DialogFragment**：继承BaseDialogFragment
4. **优化字体切换逻辑**：移除activity?.recreate()调用，改为直接应用字体

### 修改的文件
- `FontUtils.kt` - 添加DialogFragment支持
- `TpSettingsDialogFragment.kt` - 继承BaseDialogFragment
- `TpMiscSettingsFragment.kt` - 优化字体切换逻辑
- `MainMenu.kt` - 继承BaseDialogFragment
- `TpAEDialogFragment.kt` - 继承BaseDialogFragment
- `TpWBDialogFragment.kt` - 继承BaseDialogFragment
- `TpSceneDialogFragment.kt` - 继承BaseDialogFragment

## 测试步骤

### 1. 基本字体设置测试
1. 启动应用
2. 打开主菜单 → 设置按钮
3. 在设置界面中选择"杂项设置"标签
4. 在字体选择区域，依次选择：
   - 系统默认字体
   - 宋体
   - 楷体

**预期结果**：
- 每次选择字体后，设置界面的所有文字（标题、选项文本、按钮文字等）应立即更新为所选字体
- 不需要重新打开设置界面就能看到字体变化

### 2. 跨界面字体一致性测试
1. 在设置界面选择楷体
2. 切换到其他设置标签（格式设置、录制设置等）
3. 返回主界面
4. 重新打开设置界面

**预期结果**：
- 所有界面的字体应保持一致
- 字体设置应该持久化

### 3. 子对话框字体测试
1. 在设置界面选择宋体
2. 关闭设置界面
3. 打开主菜单 → 菜单按钮（弹出ISP功能菜单）
4. 点击曝光、白平衡等子功能按钮

**预期结果**：
- 所有子对话框的文字也应显示为宋体
- 字体设置应影响所有DialogFragment

### 4. Toast消息字体测试
1. 选择不同字体
2. 观察各种操作产生的Toast消息

**预期结果**：
- Toast消息的字体应与当前设置的字体一致

## 验证要点

### ✅ 成功标准
- [ ] 字体设置立即生效，无需重启应用
- [ ] 设置界面所有文字都能正确更新字体
- [ ] 主界面和所有子对话框字体保持一致
- [ ] 字体设置在应用重启后仍然有效
- [ ] 不同字体之间切换流畅，无卡顿

### ❌ 失败情况
- 字体设置后部分UI元素字体未更新
- 需要重新打开界面才能看到字体变化
- 不同界面字体不一致
- 应用崩溃或出现异常

## 技术实现细节

### FontUtils增强功能
```kotlin
// 新增DialogFragment支持
fun registerDialogFragment(dialogFragment: DialogFragment)
fun unregisterDialogFragment(dialogFragment: DialogFragment)
fun applyFontToDialogFragment(dialogFragment: DialogFragment)
```

### BaseDialogFragment基类
```kotlin
abstract class BaseDialogFragment : DialogFragment() {
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        FontUtils.registerDialogFragment(this)
        FontUtils.applyFontToDialogFragment(this)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        FontUtils.unregisterDialogFragment(this)
    }
}
```

### 字体应用机制优化
- 移除了延迟应用机制（原来的2000ms延迟）
- 改为立即应用字体到TextView
- 自动遍历ViewGroup中的所有子View

## 故障排除

### 如果字体仍未更新
1. 检查DialogFragment是否正确继承BaseDialogFragment
2. 确认FontUtils.setAppFont()被正确调用
3. 检查字体文件是否存在于res/font目录

### 如果应用崩溃
1. 检查import语句是否正确
2. 确认BaseDialogFragment类路径正确
3. 查看logcat中的错误信息

## 后续改进建议
1. 可以考虑添加字体大小设置功能
2. 支持更多字体类型
3. 添加字体预览功能
4. 优化字体加载性能
