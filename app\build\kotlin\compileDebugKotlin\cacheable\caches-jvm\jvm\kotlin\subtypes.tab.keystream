1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderEcom.touptek.xcamview.activity.ispdialogfragment.BaseISPDialogFragment,com.touptek.xcamview.util.BaseDialogFragment&com.touptek.xcamview.util.BaseFragment(androidx.appcompat.app.AppCompatActivity$androidx.fragment.app.DialogFragmentkotlin.Enum6com.touptek.xcamview.view.MeasurementOverlayView.Shapeandroid.view.View androidx.viewbinding.ViewBinding,androidx.appcompat.widget.AppCompatImageView&com.touptek.xcamview.util.BaseActivity-android.view.View.OnAttachStateChangeListenerDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener8androidx.recyclerview.widget.RecyclerView.ItemDecorationScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListenerRcom.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListenerandroidx.fragment.app.Fragment2com.touptek.video.TpIspParam.OnDataChangedListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  