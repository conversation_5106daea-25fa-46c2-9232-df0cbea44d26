package com.touptek.ui.compare;

import java.lang.System;

/**
 * 图片Matrix同步引擎
 *
 * 负责管理多个TpImageView之间的Matrix同步逻辑
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\n\b\u0016\u0018\u0000 \u001b2\u00020\u0001:\u0002\u001b\u001cB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u000f\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u00052\u0006\u0010\u0011\u001a\u00020\u0006J\u0016\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u00132\u0006\u0010\u0014\u001a\u00020\u0005H\u0014J\u0016\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00050\u00132\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u000e\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\bJ\u000e\u0010\u0018\u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\u000eJ\u0010\u0010\u001a\u001a\u00020\f2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002R\u001a\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/touptek/ui/compare/TpImageSyncEngine;", "", "()V", "imageViews", "", "", "Lcom/touptek/ui/TpImageView;", "isSyncEnabled", "", "isUpdating", "matrixListeners", "Lkotlin/Function0;", "", "syncMode", "Lcom/touptek/ui/compare/TpImageSyncEngine$SyncMode;", "addImageView", "id", "imageView", "getGroupTargets", "", "sourceId", "getTargetIds", "setSyncEnabled", "enabled", "setSyncMode", "mode", "syncMatrixFromSource", "Companion", "SyncMode", "app_debug"})
public class TpImageSyncEngine {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.ui.compare.TpImageSyncEngine.Companion Companion = null;
    private static final java.lang.String TAG = "TpImageSyncEngine";
    private final java.util.Map<java.lang.String, com.touptek.ui.TpImageView> imageViews = null;
    private final java.util.Map<java.lang.String, kotlin.jvm.functions.Function0<kotlin.Unit>> matrixListeners = null;
    private com.touptek.ui.compare.TpImageSyncEngine.SyncMode syncMode = com.touptek.ui.compare.TpImageSyncEngine.SyncMode.GLOBAL;
    private boolean isSyncEnabled = true;
    private boolean isUpdating = false;
    
    public TpImageSyncEngine() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @kotlin.jvm.JvmStatic
    public static final com.touptek.ui.compare.TpImageSyncEngine create() {
        return null;
    }
    
    /**
     * 添加需要同步的ImageView
     */
    public final void addImageView(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    com.touptek.ui.TpImageView imageView) {
    }
    
    /**
     * 设置同步模式
     */
    public final void setSyncMode(@org.jetbrains.annotations.NotNull
    com.touptek.ui.compare.TpImageSyncEngine.SyncMode mode) {
    }
    
    /**
     * 启用/禁用同步
     */
    public final void setSyncEnabled(boolean enabled) {
    }
    
    /**
     * 从源ImageView同步Matrix到其他ImageView
     */
    private final void syncMatrixFromSource(java.lang.String sourceId) {
    }
    
    /**
     * 根据同步模式获取目标ImageView ID列表
     */
    private final java.util.List<java.lang.String> getTargetIds(java.lang.String sourceId) {
        return null;
    }
    
    /**
     * 获取分组同步的目标列表
     * 可以根据具体需求重写此方法
     */
    @org.jetbrains.annotations.NotNull
    protected java.util.List<java.lang.String> getGroupTargets(@org.jetbrains.annotations.NotNull
    java.lang.String sourceId) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/touptek/ui/compare/TpImageSyncEngine$SyncMode;", "", "(Ljava/lang/String;I)V", "GLOBAL", "GROUP", "INDEPENDENT", "app_debug"})
    public static enum SyncMode {
        /*public static final*/ GLOBAL /* = new GLOBAL() */,
        /*public static final*/ GROUP /* = new GROUP() */,
        /*public static final*/ INDEPENDENT /* = new INDEPENDENT() */;
        
        SyncMode() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0005\u001a\u00020\u0006H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/ui/compare/TpImageSyncEngine$Companion;", "", "()V", "TAG", "", "create", "Lcom/touptek/ui/compare/TpImageSyncEngine;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        @kotlin.jvm.JvmStatic
        public final com.touptek.ui.compare.TpImageSyncEngine create() {
            return null;
        }
    }
}