// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTouptekBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button buttonCapture;

  @NonNull
  public final Button buttonDefaultValue;

  @NonNull
  public final Button buttonDelete;

  @NonNull
  public final Button buttonOneClickWhiteBalance;

  @NonNull
  public final Button buttonRecord;

  @NonNull
  public final Button buttonSave;

  @NonNull
  public final TextView cameraControlPanel;

  @NonNull
  public final RadioButton radioAc50hz;

  @NonNull
  public final RadioButton radioAc60hz;

  @NonNull
  public final RadioButton radioAuto;

  @NonNull
  public final RadioButton radioDc;

  @NonNull
  public final RadioGroup radioGroupPower;

  @NonNull
  public final RadioGroup radioGroupWhiteBalance;

  @NonNull
  public final RadioButton radioManual;

  @NonNull
  public final RadioButton radioRoi;

  @NonNull
  public final SeekBar seekbarBlue;

  @NonNull
  public final SeekBar seekbarExposureCompensation;

  @NonNull
  public final SeekBar seekbarExposureTime;

  @NonNull
  public final SeekBar seekbarGain;

  @NonNull
  public final SeekBar seekbarGreen;

  @NonNull
  public final SeekBar seekbarHue;

  @NonNull
  public final SeekBar seekbarRed;

  @NonNull
  public final SeekBar seekbarSaturation;

  @NonNull
  public final SeekBar seekbarSharpness;

  @NonNull
  public final Spinner spinnerScene;

  @NonNull
  public final Switch switchAutoExposure;

  @NonNull
  public final TextView textBlue;

  @NonNull
  public final TextView textExposureCompensation;

  @NonNull
  public final TextView textExposureTime;

  @NonNull
  public final TextView textGain;

  @NonNull
  public final TextView textGreen;

  @NonNull
  public final TextView textHue;

  @NonNull
  public final TextView textRed;

  @NonNull
  public final TextView textSaturation;

  @NonNull
  public final TextView textSerialStatus;

  @NonNull
  public final TextView textSharpness;

  @NonNull
  public final TextView textVersionInfo;

  private ActivityTouptekBinding(@NonNull LinearLayout rootView, @NonNull Button buttonCapture,
      @NonNull Button buttonDefaultValue, @NonNull Button buttonDelete,
      @NonNull Button buttonOneClickWhiteBalance, @NonNull Button buttonRecord,
      @NonNull Button buttonSave, @NonNull TextView cameraControlPanel,
      @NonNull RadioButton radioAc50hz, @NonNull RadioButton radioAc60hz,
      @NonNull RadioButton radioAuto, @NonNull RadioButton radioDc,
      @NonNull RadioGroup radioGroupPower, @NonNull RadioGroup radioGroupWhiteBalance,
      @NonNull RadioButton radioManual, @NonNull RadioButton radioRoi, @NonNull SeekBar seekbarBlue,
      @NonNull SeekBar seekbarExposureCompensation, @NonNull SeekBar seekbarExposureTime,
      @NonNull SeekBar seekbarGain, @NonNull SeekBar seekbarGreen, @NonNull SeekBar seekbarHue,
      @NonNull SeekBar seekbarRed, @NonNull SeekBar seekbarSaturation,
      @NonNull SeekBar seekbarSharpness, @NonNull Spinner spinnerScene,
      @NonNull Switch switchAutoExposure, @NonNull TextView textBlue,
      @NonNull TextView textExposureCompensation, @NonNull TextView textExposureTime,
      @NonNull TextView textGain, @NonNull TextView textGreen, @NonNull TextView textHue,
      @NonNull TextView textRed, @NonNull TextView textSaturation,
      @NonNull TextView textSerialStatus, @NonNull TextView textSharpness,
      @NonNull TextView textVersionInfo) {
    this.rootView = rootView;
    this.buttonCapture = buttonCapture;
    this.buttonDefaultValue = buttonDefaultValue;
    this.buttonDelete = buttonDelete;
    this.buttonOneClickWhiteBalance = buttonOneClickWhiteBalance;
    this.buttonRecord = buttonRecord;
    this.buttonSave = buttonSave;
    this.cameraControlPanel = cameraControlPanel;
    this.radioAc50hz = radioAc50hz;
    this.radioAc60hz = radioAc60hz;
    this.radioAuto = radioAuto;
    this.radioDc = radioDc;
    this.radioGroupPower = radioGroupPower;
    this.radioGroupWhiteBalance = radioGroupWhiteBalance;
    this.radioManual = radioManual;
    this.radioRoi = radioRoi;
    this.seekbarBlue = seekbarBlue;
    this.seekbarExposureCompensation = seekbarExposureCompensation;
    this.seekbarExposureTime = seekbarExposureTime;
    this.seekbarGain = seekbarGain;
    this.seekbarGreen = seekbarGreen;
    this.seekbarHue = seekbarHue;
    this.seekbarRed = seekbarRed;
    this.seekbarSaturation = seekbarSaturation;
    this.seekbarSharpness = seekbarSharpness;
    this.spinnerScene = spinnerScene;
    this.switchAutoExposure = switchAutoExposure;
    this.textBlue = textBlue;
    this.textExposureCompensation = textExposureCompensation;
    this.textExposureTime = textExposureTime;
    this.textGain = textGain;
    this.textGreen = textGreen;
    this.textHue = textHue;
    this.textRed = textRed;
    this.textSaturation = textSaturation;
    this.textSerialStatus = textSerialStatus;
    this.textSharpness = textSharpness;
    this.textVersionInfo = textVersionInfo;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTouptekBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTouptekBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_touptek, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTouptekBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_capture;
      Button buttonCapture = ViewBindings.findChildViewById(rootView, id);
      if (buttonCapture == null) {
        break missingId;
      }

      id = R.id.button_default_value;
      Button buttonDefaultValue = ViewBindings.findChildViewById(rootView, id);
      if (buttonDefaultValue == null) {
        break missingId;
      }

      id = R.id.button_delete;
      Button buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.button_one_click_white_balance;
      Button buttonOneClickWhiteBalance = ViewBindings.findChildViewById(rootView, id);
      if (buttonOneClickWhiteBalance == null) {
        break missingId;
      }

      id = R.id.button_record;
      Button buttonRecord = ViewBindings.findChildViewById(rootView, id);
      if (buttonRecord == null) {
        break missingId;
      }

      id = R.id.button_save;
      Button buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.camera_control_panel;
      TextView cameraControlPanel = ViewBindings.findChildViewById(rootView, id);
      if (cameraControlPanel == null) {
        break missingId;
      }

      id = R.id.radio_ac_50hz;
      RadioButton radioAc50hz = ViewBindings.findChildViewById(rootView, id);
      if (radioAc50hz == null) {
        break missingId;
      }

      id = R.id.radio_ac_60hz;
      RadioButton radioAc60hz = ViewBindings.findChildViewById(rootView, id);
      if (radioAc60hz == null) {
        break missingId;
      }

      id = R.id.radio_auto;
      RadioButton radioAuto = ViewBindings.findChildViewById(rootView, id);
      if (radioAuto == null) {
        break missingId;
      }

      id = R.id.radio_dc;
      RadioButton radioDc = ViewBindings.findChildViewById(rootView, id);
      if (radioDc == null) {
        break missingId;
      }

      id = R.id.radio_group_power;
      RadioGroup radioGroupPower = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupPower == null) {
        break missingId;
      }

      id = R.id.radio_group_white_balance;
      RadioGroup radioGroupWhiteBalance = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupWhiteBalance == null) {
        break missingId;
      }

      id = R.id.radio_manual;
      RadioButton radioManual = ViewBindings.findChildViewById(rootView, id);
      if (radioManual == null) {
        break missingId;
      }

      id = R.id.radio_roi;
      RadioButton radioRoi = ViewBindings.findChildViewById(rootView, id);
      if (radioRoi == null) {
        break missingId;
      }

      id = R.id.seekbar_blue;
      SeekBar seekbarBlue = ViewBindings.findChildViewById(rootView, id);
      if (seekbarBlue == null) {
        break missingId;
      }

      id = R.id.seekbar_exposure_compensation;
      SeekBar seekbarExposureCompensation = ViewBindings.findChildViewById(rootView, id);
      if (seekbarExposureCompensation == null) {
        break missingId;
      }

      id = R.id.seekbar_exposure_time;
      SeekBar seekbarExposureTime = ViewBindings.findChildViewById(rootView, id);
      if (seekbarExposureTime == null) {
        break missingId;
      }

      id = R.id.seekbar_gain;
      SeekBar seekbarGain = ViewBindings.findChildViewById(rootView, id);
      if (seekbarGain == null) {
        break missingId;
      }

      id = R.id.seekbar_green;
      SeekBar seekbarGreen = ViewBindings.findChildViewById(rootView, id);
      if (seekbarGreen == null) {
        break missingId;
      }

      id = R.id.seekbar_hue;
      SeekBar seekbarHue = ViewBindings.findChildViewById(rootView, id);
      if (seekbarHue == null) {
        break missingId;
      }

      id = R.id.seekbar_red;
      SeekBar seekbarRed = ViewBindings.findChildViewById(rootView, id);
      if (seekbarRed == null) {
        break missingId;
      }

      id = R.id.seekbar_saturation;
      SeekBar seekbarSaturation = ViewBindings.findChildViewById(rootView, id);
      if (seekbarSaturation == null) {
        break missingId;
      }

      id = R.id.seekbar_sharpness;
      SeekBar seekbarSharpness = ViewBindings.findChildViewById(rootView, id);
      if (seekbarSharpness == null) {
        break missingId;
      }

      id = R.id.spinner_scene;
      Spinner spinnerScene = ViewBindings.findChildViewById(rootView, id);
      if (spinnerScene == null) {
        break missingId;
      }

      id = R.id.switch_auto_exposure;
      Switch switchAutoExposure = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoExposure == null) {
        break missingId;
      }

      id = R.id.text_blue;
      TextView textBlue = ViewBindings.findChildViewById(rootView, id);
      if (textBlue == null) {
        break missingId;
      }

      id = R.id.text_exposure_compensation;
      TextView textExposureCompensation = ViewBindings.findChildViewById(rootView, id);
      if (textExposureCompensation == null) {
        break missingId;
      }

      id = R.id.text_exposure_time;
      TextView textExposureTime = ViewBindings.findChildViewById(rootView, id);
      if (textExposureTime == null) {
        break missingId;
      }

      id = R.id.text_gain;
      TextView textGain = ViewBindings.findChildViewById(rootView, id);
      if (textGain == null) {
        break missingId;
      }

      id = R.id.text_green;
      TextView textGreen = ViewBindings.findChildViewById(rootView, id);
      if (textGreen == null) {
        break missingId;
      }

      id = R.id.text_hue;
      TextView textHue = ViewBindings.findChildViewById(rootView, id);
      if (textHue == null) {
        break missingId;
      }

      id = R.id.text_red;
      TextView textRed = ViewBindings.findChildViewById(rootView, id);
      if (textRed == null) {
        break missingId;
      }

      id = R.id.text_saturation;
      TextView textSaturation = ViewBindings.findChildViewById(rootView, id);
      if (textSaturation == null) {
        break missingId;
      }

      id = R.id.text_serial_status;
      TextView textSerialStatus = ViewBindings.findChildViewById(rootView, id);
      if (textSerialStatus == null) {
        break missingId;
      }

      id = R.id.text_sharpness;
      TextView textSharpness = ViewBindings.findChildViewById(rootView, id);
      if (textSharpness == null) {
        break missingId;
      }

      id = R.id.text_version_info;
      TextView textVersionInfo = ViewBindings.findChildViewById(rootView, id);
      if (textVersionInfo == null) {
        break missingId;
      }

      return new ActivityTouptekBinding((LinearLayout) rootView, buttonCapture, buttonDefaultValue,
          buttonDelete, buttonOneClickWhiteBalance, buttonRecord, buttonSave, cameraControlPanel,
          radioAc50hz, radioAc60hz, radioAuto, radioDc, radioGroupPower, radioGroupWhiteBalance,
          radioManual, radioRoi, seekbarBlue, seekbarExposureCompensation, seekbarExposureTime,
          seekbarGain, seekbarGreen, seekbarHue, seekbarRed, seekbarSaturation, seekbarSharpness,
          spinnerScene, switchAutoExposure, textBlue, textExposureCompensation, textExposureTime,
          textGain, textGreen, textHue, textRed, textSaturation, textSerialStatus, textSharpness,
          textVersionInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
