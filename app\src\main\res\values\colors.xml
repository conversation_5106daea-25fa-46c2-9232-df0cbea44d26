<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- 添加以下缺失的颜色资源定义 -->
    <color name="black">#FF000000</color>
    <color name="white_background">#FFFFFF</color>
    <color name="grey_background">#AAAAAA</color>
    <color name="border_color">#999999</color>
    <color name="solid_blue">#0000FF</color>
    <color name="Light_black">#FF2E2E2E</color>
    <color name="popup_text_color">#FFFFFF</color>
    <color name="popup_background_color">#555555</color>
    <color name="gray_text_disabled">#A0A0A0</color>
    <color name="gray_icon_disabled">#B0B0B0</color>

    <color name="colorPrimary">#FFA0A0A0</color> <!-- 边框颜色 -->
    <color name="colorISPText">#333333</color>
    <color name="colorISPBlue">#FF2196F3</color>


    <!-- Switch -->
    <color name="blue_500">#FF2196F3</color> <!-- 主蓝色 -->
    <color name="gray_300">#FFE0E0E0</color> <!-- 默认灰色 -->
    <color name="white">#FFFFFFFF</color> <!-- 白色 -->
    <!--        -->

    <!-- Dialog and Button Colors -->
    <color name="dialog_border">#888888</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="gray_text">#666666</color>

</resources>