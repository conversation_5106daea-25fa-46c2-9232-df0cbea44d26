// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentFormatSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCompatibilityTest;

  @NonNull
  public final Button btnParamDetails;

  @NonNull
  public final Button btnResetSettings;

  @NonNull
  public final Button btnStylePreview;

  @NonNull
  public final CheckBox checkFusionMode;

  @NonNull
  public final CheckBox checkLayerMode;

  @NonNull
  public final CheckBox checkSaveMeasurementMarks;

  @NonNull
  public final CheckBox checkSaveMeasurementReport;

  @NonNull
  public final CheckBox checkSaveMeasurementValues;

  @NonNull
  public final RadioGroup colorDepthGroup;

  @NonNull
  public final RadioGroup colorSpaceGroup;

  @NonNull
  public final RadioGroup formatGroup;

  @NonNull
  public final RadioButton radio16bit;

  @NonNull
  public final RadioButton radio8bit;

  @NonNull
  public final RadioButton radioAdobeRgb;

  @NonNull
  public final RadioButton radioBmp;

  @NonNull
  public final RadioButton radioFusionMode;

  @NonNull
  public final RadioButton radioJpeg;

  @NonNull
  public final RadioButton radioLayerMode;

  @NonNull
  public final RadioButton radioPng;

  @NonNull
  public final RadioButton radioSrgb;

  @NonNull
  public final RadioButton radioTiff;

  @NonNull
  public final RadioGroup saveModeGroup;

  @NonNull
  public final Spinner spinnerCompression;

  @NonNull
  public final Spinner spinnerFontSize;

  @NonNull
  public final Spinner spinnerLabelStyle;

  private FragmentFormatSettingsBinding(@NonNull ScrollView rootView,
      @NonNull Button btnCompatibilityTest, @NonNull Button btnParamDetails,
      @NonNull Button btnResetSettings, @NonNull Button btnStylePreview,
      @NonNull CheckBox checkFusionMode, @NonNull CheckBox checkLayerMode,
      @NonNull CheckBox checkSaveMeasurementMarks, @NonNull CheckBox checkSaveMeasurementReport,
      @NonNull CheckBox checkSaveMeasurementValues, @NonNull RadioGroup colorDepthGroup,
      @NonNull RadioGroup colorSpaceGroup, @NonNull RadioGroup formatGroup,
      @NonNull RadioButton radio16bit, @NonNull RadioButton radio8bit,
      @NonNull RadioButton radioAdobeRgb, @NonNull RadioButton radioBmp,
      @NonNull RadioButton radioFusionMode, @NonNull RadioButton radioJpeg,
      @NonNull RadioButton radioLayerMode, @NonNull RadioButton radioPng,
      @NonNull RadioButton radioSrgb, @NonNull RadioButton radioTiff,
      @NonNull RadioGroup saveModeGroup, @NonNull Spinner spinnerCompression,
      @NonNull Spinner spinnerFontSize, @NonNull Spinner spinnerLabelStyle) {
    this.rootView = rootView;
    this.btnCompatibilityTest = btnCompatibilityTest;
    this.btnParamDetails = btnParamDetails;
    this.btnResetSettings = btnResetSettings;
    this.btnStylePreview = btnStylePreview;
    this.checkFusionMode = checkFusionMode;
    this.checkLayerMode = checkLayerMode;
    this.checkSaveMeasurementMarks = checkSaveMeasurementMarks;
    this.checkSaveMeasurementReport = checkSaveMeasurementReport;
    this.checkSaveMeasurementValues = checkSaveMeasurementValues;
    this.colorDepthGroup = colorDepthGroup;
    this.colorSpaceGroup = colorSpaceGroup;
    this.formatGroup = formatGroup;
    this.radio16bit = radio16bit;
    this.radio8bit = radio8bit;
    this.radioAdobeRgb = radioAdobeRgb;
    this.radioBmp = radioBmp;
    this.radioFusionMode = radioFusionMode;
    this.radioJpeg = radioJpeg;
    this.radioLayerMode = radioLayerMode;
    this.radioPng = radioPng;
    this.radioSrgb = radioSrgb;
    this.radioTiff = radioTiff;
    this.saveModeGroup = saveModeGroup;
    this.spinnerCompression = spinnerCompression;
    this.spinnerFontSize = spinnerFontSize;
    this.spinnerLabelStyle = spinnerLabelStyle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentFormatSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentFormatSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_format_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentFormatSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_compatibility_test;
      Button btnCompatibilityTest = ViewBindings.findChildViewById(rootView, id);
      if (btnCompatibilityTest == null) {
        break missingId;
      }

      id = R.id.btn_param_details;
      Button btnParamDetails = ViewBindings.findChildViewById(rootView, id);
      if (btnParamDetails == null) {
        break missingId;
      }

      id = R.id.btn_reset_settings;
      Button btnResetSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnResetSettings == null) {
        break missingId;
      }

      id = R.id.btn_style_preview;
      Button btnStylePreview = ViewBindings.findChildViewById(rootView, id);
      if (btnStylePreview == null) {
        break missingId;
      }

      id = R.id.check_fusion_mode;
      CheckBox checkFusionMode = ViewBindings.findChildViewById(rootView, id);
      if (checkFusionMode == null) {
        break missingId;
      }

      id = R.id.check_layer_mode;
      CheckBox checkLayerMode = ViewBindings.findChildViewById(rootView, id);
      if (checkLayerMode == null) {
        break missingId;
      }

      id = R.id.check_save_measurement_marks;
      CheckBox checkSaveMeasurementMarks = ViewBindings.findChildViewById(rootView, id);
      if (checkSaveMeasurementMarks == null) {
        break missingId;
      }

      id = R.id.check_save_measurement_report;
      CheckBox checkSaveMeasurementReport = ViewBindings.findChildViewById(rootView, id);
      if (checkSaveMeasurementReport == null) {
        break missingId;
      }

      id = R.id.check_save_measurement_values;
      CheckBox checkSaveMeasurementValues = ViewBindings.findChildViewById(rootView, id);
      if (checkSaveMeasurementValues == null) {
        break missingId;
      }

      id = R.id.color_depth_group;
      RadioGroup colorDepthGroup = ViewBindings.findChildViewById(rootView, id);
      if (colorDepthGroup == null) {
        break missingId;
      }

      id = R.id.color_space_group;
      RadioGroup colorSpaceGroup = ViewBindings.findChildViewById(rootView, id);
      if (colorSpaceGroup == null) {
        break missingId;
      }

      id = R.id.format_group;
      RadioGroup formatGroup = ViewBindings.findChildViewById(rootView, id);
      if (formatGroup == null) {
        break missingId;
      }

      id = R.id.radio_16bit;
      RadioButton radio16bit = ViewBindings.findChildViewById(rootView, id);
      if (radio16bit == null) {
        break missingId;
      }

      id = R.id.radio_8bit;
      RadioButton radio8bit = ViewBindings.findChildViewById(rootView, id);
      if (radio8bit == null) {
        break missingId;
      }

      id = R.id.radio_adobe_rgb;
      RadioButton radioAdobeRgb = ViewBindings.findChildViewById(rootView, id);
      if (radioAdobeRgb == null) {
        break missingId;
      }

      id = R.id.radio_bmp;
      RadioButton radioBmp = ViewBindings.findChildViewById(rootView, id);
      if (radioBmp == null) {
        break missingId;
      }

      id = R.id.radio_fusion_mode;
      RadioButton radioFusionMode = ViewBindings.findChildViewById(rootView, id);
      if (radioFusionMode == null) {
        break missingId;
      }

      id = R.id.radio_jpeg;
      RadioButton radioJpeg = ViewBindings.findChildViewById(rootView, id);
      if (radioJpeg == null) {
        break missingId;
      }

      id = R.id.radio_layer_mode;
      RadioButton radioLayerMode = ViewBindings.findChildViewById(rootView, id);
      if (radioLayerMode == null) {
        break missingId;
      }

      id = R.id.radio_png;
      RadioButton radioPng = ViewBindings.findChildViewById(rootView, id);
      if (radioPng == null) {
        break missingId;
      }

      id = R.id.radio_srgb;
      RadioButton radioSrgb = ViewBindings.findChildViewById(rootView, id);
      if (radioSrgb == null) {
        break missingId;
      }

      id = R.id.radio_tiff;
      RadioButton radioTiff = ViewBindings.findChildViewById(rootView, id);
      if (radioTiff == null) {
        break missingId;
      }

      id = R.id.save_mode_group;
      RadioGroup saveModeGroup = ViewBindings.findChildViewById(rootView, id);
      if (saveModeGroup == null) {
        break missingId;
      }

      id = R.id.spinner_compression;
      Spinner spinnerCompression = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCompression == null) {
        break missingId;
      }

      id = R.id.spinner_font_size;
      Spinner spinnerFontSize = ViewBindings.findChildViewById(rootView, id);
      if (spinnerFontSize == null) {
        break missingId;
      }

      id = R.id.spinner_label_style;
      Spinner spinnerLabelStyle = ViewBindings.findChildViewById(rootView, id);
      if (spinnerLabelStyle == null) {
        break missingId;
      }

      return new FragmentFormatSettingsBinding((ScrollView) rootView, btnCompatibilityTest,
          btnParamDetails, btnResetSettings, btnStylePreview, checkFusionMode, checkLayerMode,
          checkSaveMeasurementMarks, checkSaveMeasurementReport, checkSaveMeasurementValues,
          colorDepthGroup, colorSpaceGroup, formatGroup, radio16bit, radio8bit, radioAdobeRgb,
          radioBmp, radioFusionMode, radioJpeg, radioLayerMode, radioPng, radioSrgb, radioTiff,
          saveModeGroup, spinnerCompression, spinnerFontSize, spinnerLabelStyle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
