<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="com.obiscr.chatgpt.settings.EasyCodeState">
    <option name="projectFiles" value="$PROJECT_DIR$/app/.cxx/Debug/1z4oy32e/arm64-v8a/.cmake/api/v1/reply/cache-v2-a89e1f569e9c60455ce6.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/.cmake/api/v1/reply/cmakeFiles-v1-0a354f321f42fe2c4b43.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/.cmake/api/v1/reply/codemodel-v2-d8061d162d58ed411106.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/.cmake/api/v1/reply/directory-.-Debug-d0094a50bb2071803777.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/.cmake/api/v1/reply/index-2025-07-24T06-26-01-0639.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdC/CMakeCCompilerId.c;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdCXX/CMakeCXXCompilerId.cpp;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/android_gradle_build.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/android_gradle_build_mini.json;E:/rk/AndroidStudio/XCamView/app/.cxx/Debug/1z4oy32e/arm64-v8a/prefab_config.json;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ActivityMainBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ActivityTouptekBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ActivityTouptekBtnBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ActivityWelcomeBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/AutoaeLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/BrowseGridLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/BrowseLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/CopydialogSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/DialogFileDetailsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/DialogModernSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/DialogSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/FlipLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/FolderItemBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/FragmentFormatSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/FragmentMeasurementSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/FragmentNetworkSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/FragmentStorageSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/HzLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ImageParameter2LayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ImageParameterLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/ImageViewerBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/LayoutInputInfoItemBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/MeasurementLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/OperationGridLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/PopupConfigMenuBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/PopupMenuLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/RightPanelLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/SettingsMiscBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/SettingsRecordBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/TestdialogSettingsBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/VideodecodeLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/VideoLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/generated/data_binding_base_class_source_out/debug/out/com/touptek/xcamview/databinding/WhitebalanceLayoutBinding.java;E:/rk/AndroidStudio/XCamView/app/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/apk/debug/output-metadata.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/compatible_screen_manifest/debug/createDebugCompatibleScreenManifests/output-metadata.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/cxx/Debug/1z4oy32e/logs/arm64-v8a/build_model.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/cxx/Debug/1z4oy32e/logs/arm64-v8a/metadata_generation_record.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/data_binding_base_class_log_artifact/debug/dataBindingGenBaseClassesDebug/out/com.touptek.xcamview-binding_classes.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/incremental/dataBindingGenBaseClassesDebug/base_builder_log.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/linked_resources_binary_format/debug/processDebugResources/output-metadata.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_manifests/debug/processDebugManifest/output-metadata.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/mergeDebugResources.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-af.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-am.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ar.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-as.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-az.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-b+es+419.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-b+sr+Latn.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-be.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-bg.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-bn.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-bs.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ca.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-cs.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-da.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-de.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-el.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-en-rAU.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-en-rCA.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-en-rGB.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-en-rIN.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-en-rXC.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-es-rUS.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-es.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-et.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-eu.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-fa.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-fi.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-fr-rCA.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-fr.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-gl.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-gu.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-h320dp-port-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-h360dp-land-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-h480dp-land-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-h550dp-port-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-h720dp-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-hdpi-v4.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-hi.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-hr.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-hu.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-hy.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-in.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-is.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-it.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-iw.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ja.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ka.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-kk.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-km.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-kn.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ko.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ky.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-land.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-large-v4.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ldltr-v21.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ldrtl-v17.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-lo.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-lt.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-lv.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-mk.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ml.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-mn.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-mr.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ms.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-my.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-nb.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ne.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-night-v8.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-nl.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-or.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-pa.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-pl.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-port.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-pt-rBR.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-pt-rPT.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-pt.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ro.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ru.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-si.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sk.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sl.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-small-v4.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sq.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sr.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sv.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sw.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-sw600dp-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ta.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-te.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-th.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-tl.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-tr.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-uk.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-ur.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-uz.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v16.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v17.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v18.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v21.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v22.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v23.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v24.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v25.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v26.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v28.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-v31.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-vi.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-w320dp-land-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-w360dp-port-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-w400dp-port-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-w600dp-land-v13.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-watch-v20.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-watch-v21.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-xlarge-v4.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-zh-rCN.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-zh-rHK.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-zh-rTW.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values-zu.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/mergeDebugResources.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/packaged_manifests/debug/processDebugManifestForPackage/output-metadata.json;E:/rk/AndroidStudio/XCamView/app/build/intermediates/signing_config_versions/debug/writeDebugSigningConfigVersions/signing-config-versions.json;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/android/rockchip/camera2/view/OverlayView.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/videomanagement/TpVideoDecoderDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/TpCopyDirDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/TpOperationDirAdapter.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/TpThumbGridAdapter.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/TpThumbSpacingDecoration.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/browse/TpVideoBrowse.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/wbroimanagement/TpRectangleOverlayView.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/TpAEDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/TpFlipDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/TpHzDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/TpImageProcess2DialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/TpImageProcessDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/ispdialogfragment/TpWBDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/measurement/TpMeasurementDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpFormatSettingsFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpMeasurementSettingsFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpMiscSettingsFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpNetworkSettingsFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpRecordSettingsFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpSettingsDialogFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/settings/TpStorageSettingsFragment.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/MainActivity.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/MainMenu.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/activity/StatusBanner.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/util/BaseActivity.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/util/FontUtils.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/util/PathUtilsKt.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/util/TpExtensionsKt.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/com/touptek/xcamview/view/MeasurementOverlayView.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/error/NonExistentClass.java;E:/rk/AndroidStudio/XCamView/app/build/tmp/kapt3/stubs/debug/FolderAdapter.java;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/videomanagement/TpVideoDecoderDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/FolderAdapter.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/TpCopyDirDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/TpOperationDirAdapter.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/TpThumbGridAdapter.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/TpThumbSpacingDecoration.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/browse/TpVideoBrowse.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/wbroimanagement/TpRectangleOverlayView.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/TpAEDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/TpFlipDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/TpHzDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/TpImageProcess2DialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/TpImageProcessDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/ispdialogfragment/TpWBDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/measurement/TpMeasureDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/measurement/TpMeasurementDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpFormatSettingsFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpMeasurementSettingsFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpMiscSettingsFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpNetworkSettingsFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpRecordSettingsFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpSettingsDialogFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/settings/TpStorageSettingsFragment.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/MainActivity.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/MainMenu.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/activity/OverlayView.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/util/FontUtils.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/util/PathUtils.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/util/TpExtensions.kt;E:/rk/AndroidStudio/XCamView/app/src/main/java/com/touptek/xcamview/view/MeasurementOverlayView.kt;E:/rk/AndroidStudio/XCamView/app/build.gradle.kts;E:/rk/AndroidStudio/XCamView/build.gradle.kts;E:/rk/AndroidStudio/XCamView/settings.gradle.kts" />
  </component>
</project>