<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 - 白色边框 + 轻微白色填充提供反馈 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#20FFFFFF" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- 禁用状态 - 半透明白色边框 + 极淡填充 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#08FFFFFF" />
            <stroke
                android:width="2dp"
                android:color="#60FFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- 正常状态 - 白色边框 + 透明内部（空心效果） -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
