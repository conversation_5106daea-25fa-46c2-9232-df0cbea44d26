<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#F0F0F0" />
            <corners android:radius="10dp" />
            <stroke android:width="2dp" android:color="#2196F3" />
        </shape>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF" />
            <corners android:radius="10dp" />
            <stroke android:width="1dp" android:color="#CCCCCC" />
        </shape>
    </item>
</selector>
