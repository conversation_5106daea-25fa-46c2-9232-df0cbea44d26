// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ImageParameter2LayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnDefaultImageParameter2;

  @NonNull
  public final ImageButton btnDenoiseAdd;

  @NonNull
  public final ImageButton btnDenoiseReduce;

  @NonNull
  public final ImageButton btnSharpnessAdd;

  @NonNull
  public final ImageButton btnSharpnessReduce;

  @NonNull
  public final SeekBar seekbarDenoiseTv;

  @NonNull
  public final SeekBar seekbarSharpnessTv;

  @NonNull
  public final TextView textDenoiseValue;

  @NonNull
  public final TextView textSharpnessValue;

  private ImageParameter2LayoutBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnDefaultImageParameter2, @NonNull ImageButton btnDenoiseAdd,
      @NonNull ImageButton btnDenoiseReduce, @NonNull ImageButton btnSharpnessAdd,
      @NonNull ImageButton btnSharpnessReduce, @NonNull SeekBar seekbarDenoiseTv,
      @NonNull SeekBar seekbarSharpnessTv, @NonNull TextView textDenoiseValue,
      @NonNull TextView textSharpnessValue) {
    this.rootView = rootView;
    this.btnDefaultImageParameter2 = btnDefaultImageParameter2;
    this.btnDenoiseAdd = btnDenoiseAdd;
    this.btnDenoiseReduce = btnDenoiseReduce;
    this.btnSharpnessAdd = btnSharpnessAdd;
    this.btnSharpnessReduce = btnSharpnessReduce;
    this.seekbarDenoiseTv = seekbarDenoiseTv;
    this.seekbarSharpnessTv = seekbarSharpnessTv;
    this.textDenoiseValue = textDenoiseValue;
    this.textSharpnessValue = textSharpnessValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ImageParameter2LayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ImageParameter2LayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.image_parameter_2_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ImageParameter2LayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_Default_image_parameter_2;
      Button btnDefaultImageParameter2 = ViewBindings.findChildViewById(rootView, id);
      if (btnDefaultImageParameter2 == null) {
        break missingId;
      }

      id = R.id.btn_denoise_add;
      ImageButton btnDenoiseAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnDenoiseAdd == null) {
        break missingId;
      }

      id = R.id.btn_denoise_reduce;
      ImageButton btnDenoiseReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnDenoiseReduce == null) {
        break missingId;
      }

      id = R.id.btn_sharpness_add;
      ImageButton btnSharpnessAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnSharpnessAdd == null) {
        break missingId;
      }

      id = R.id.btn_sharpness_reduce;
      ImageButton btnSharpnessReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnSharpnessReduce == null) {
        break missingId;
      }

      id = R.id.seekbar_denoise_tv;
      SeekBar seekbarDenoiseTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarDenoiseTv == null) {
        break missingId;
      }

      id = R.id.seekbar_sharpness_tv;
      SeekBar seekbarSharpnessTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarSharpnessTv == null) {
        break missingId;
      }

      id = R.id.text_denoise_value;
      TextView textDenoiseValue = ViewBindings.findChildViewById(rootView, id);
      if (textDenoiseValue == null) {
        break missingId;
      }

      id = R.id.text_sharpness_value;
      TextView textSharpnessValue = ViewBindings.findChildViewById(rootView, id);
      if (textSharpnessValue == null) {
        break missingId;
      }

      return new ImageParameter2LayoutBinding((LinearLayout) rootView, btnDefaultImageParameter2,
          btnDenoiseAdd, btnDenoiseReduce, btnSharpnessAdd, btnSharpnessReduce, seekbarDenoiseTv,
          seekbarSharpnessTv, textDenoiseValue, textSharpnessValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
