<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 - 灰色条形 (添加左右间距) -->
    <item android:state_selected="true">
        <layer-list>
        <!-- 条形背景 - 现在有左右边距 -->
        <item
            android:top="8dp"
            android:bottom="8dp"
            android:left="8dp"
            android:right="8dp">
        <shape android:shape="rectangle">
            <solid android:color="#A0A0A0"/> <!-- 灰色 -->
            <corners android:radius="8dp"/> <!-- 圆角 -->
        </shape>
    </item>
</layer-list>
    </item>
    <!-- 默认状态 -->
    <item>
        <color android:color="@android:color/transparent"/>
    </item>
</selector>