// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class VideoLayoutBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button btnRecord;

  @NonNull
  public final Button btnReturn;

  @NonNull
  public final Button btnSwitch;

  @NonNull
  public final TextureView textureView;

  private VideoLayoutBinding(@NonNull FrameLayout rootView, @NonNull Button btnRecord,
      @NonNull Button btnReturn, @NonNull Button btnSwitch, @NonNull TextureView textureView) {
    this.rootView = rootView;
    this.btnRecord = btnRecord;
    this.btnReturn = btnReturn;
    this.btnSwitch = btnSwitch;
    this.textureView = textureView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static VideoLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static VideoLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.video_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static VideoLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_record;
      Button btnRecord = ViewBindings.findChildViewById(rootView, id);
      if (btnRecord == null) {
        break missingId;
      }

      id = R.id.btn_return;
      Button btnReturn = ViewBindings.findChildViewById(rootView, id);
      if (btnReturn == null) {
        break missingId;
      }

      id = R.id.btn_switch;
      Button btnSwitch = ViewBindings.findChildViewById(rootView, id);
      if (btnSwitch == null) {
        break missingId;
      }

      id = R.id.texture_view;
      TextureView textureView = ViewBindings.findChildViewById(rootView, id);
      if (textureView == null) {
        break missingId;
      }

      return new VideoLayoutBinding((FrameLayout) rootView, btnRecord, btnReturn, btnSwitch,
          textureView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
