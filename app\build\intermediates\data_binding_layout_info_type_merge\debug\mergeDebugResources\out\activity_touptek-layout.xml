<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_touptek" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_touptek.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_touptek_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="483" endOffset="14"/></Target><Target id="@+id/text_serial_status" view="TextView"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="37"/></Target><Target id="@+id/text_version_info" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="34" endOffset="37"/></Target><Target id="@+id/camera_control_panel" view="TextView"><Expressions/><location startLine="39" startOffset="4" endLine="47" endOffset="34"/></Target><Target id="@+id/button_capture" view="Button"><Expressions/><location startLine="56" startOffset="8" endLine="61" endOffset="31"/></Target><Target id="@+id/button_record" view="Button"><Expressions/><location startLine="63" startOffset="8" endLine="67" endOffset="31"/></Target><Target id="@+id/switch_auto_exposure" view="Switch"><Expressions/><location startLine="85" startOffset="8" endLine="90" endOffset="37"/></Target><Target id="@+id/text_exposure_compensation" view="TextView"><Expressions/><location startLine="107" startOffset="8" endLine="113" endOffset="30"/></Target><Target id="@+id/seekbar_exposure_compensation" view="SeekBar"><Expressions/><location startLine="117" startOffset="4" endLine="121" endOffset="26"/></Target><Target id="@+id/text_exposure_time" view="TextView"><Expressions/><location startLine="136" startOffset="8" endLine="142" endOffset="30"/></Target><Target id="@+id/seekbar_exposure_time" view="SeekBar"><Expressions/><location startLine="146" startOffset="4" endLine="150" endOffset="28"/></Target><Target id="@+id/text_gain" view="TextView"><Expressions/><location startLine="165" startOffset="8" endLine="171" endOffset="30"/></Target><Target id="@+id/seekbar_gain" view="SeekBar"><Expressions/><location startLine="175" startOffset="4" endLine="179" endOffset="26"/></Target><Target id="@+id/radio_group_white_balance" view="RadioGroup"><Expressions/><location startLine="188" startOffset="4" endLine="219" endOffset="16"/></Target><Target id="@+id/radio_auto" view="RadioButton"><Expressions/><location startLine="196" startOffset="8" endLine="202" endOffset="31"/></Target><Target id="@+id/radio_manual" view="RadioButton"><Expressions/><location startLine="204" startOffset="8" endLine="210" endOffset="31"/></Target><Target id="@+id/radio_roi" view="RadioButton"><Expressions/><location startLine="212" startOffset="8" endLine="218" endOffset="32"/></Target><Target id="@+id/text_red" view="TextView"><Expressions/><location startLine="234" startOffset="8" endLine="240" endOffset="30"/></Target><Target id="@+id/seekbar_red" view="SeekBar"><Expressions/><location startLine="244" startOffset="4" endLine="248" endOffset="28"/></Target><Target id="@+id/text_green" view="TextView"><Expressions/><location startLine="263" startOffset="8" endLine="269" endOffset="30"/></Target><Target id="@+id/seekbar_green" view="SeekBar"><Expressions/><location startLine="273" startOffset="4" endLine="277" endOffset="28"/></Target><Target id="@+id/text_blue" view="TextView"><Expressions/><location startLine="292" startOffset="8" endLine="298" endOffset="30"/></Target><Target id="@+id/seekbar_blue" view="SeekBar"><Expressions/><location startLine="302" startOffset="4" endLine="306" endOffset="28"/></Target><Target id="@+id/button_one_click_white_balance" view="Button"><Expressions/><location startLine="309" startOffset="4" endLine="314" endOffset="30"/></Target><Target id="@+id/text_sharpness" view="TextView"><Expressions/><location startLine="329" startOffset="8" endLine="335" endOffset="30"/></Target><Target id="@+id/seekbar_sharpness" view="SeekBar"><Expressions/><location startLine="339" startOffset="4" endLine="343" endOffset="27"/></Target><Target id="@+id/text_saturation" view="TextView"><Expressions/><location startLine="358" startOffset="8" endLine="364" endOffset="30"/></Target><Target id="@+id/seekbar_saturation" view="SeekBar"><Expressions/><location startLine="368" startOffset="4" endLine="372" endOffset="27"/></Target><Target id="@+id/text_hue" view="TextView"><Expressions/><location startLine="387" startOffset="8" endLine="393" endOffset="30"/></Target><Target id="@+id/seekbar_hue" view="SeekBar"><Expressions/><location startLine="397" startOffset="4" endLine="401" endOffset="27"/></Target><Target id="@+id/radio_group_power" view="RadioGroup"><Expressions/><location startLine="404" startOffset="4" endLine="432" endOffset="16"/></Target><Target id="@+id/radio_dc" view="RadioButton"><Expressions/><location startLine="411" startOffset="8" endLine="417" endOffset="31"/></Target><Target id="@+id/radio_ac_50hz" view="RadioButton"><Expressions/><location startLine="419" startOffset="8" endLine="424" endOffset="37"/></Target><Target id="@+id/radio_ac_60hz" view="RadioButton"><Expressions/><location startLine="426" startOffset="8" endLine="431" endOffset="37"/></Target><Target id="@+id/spinner_scene" view="Spinner"><Expressions/><location startLine="447" startOffset="8" endLine="451" endOffset="39"/></Target><Target id="@+id/button_save" view="Button"><Expressions/><location startLine="453" startOffset="8" endLine="458" endOffset="31"/></Target><Target id="@+id/button_delete" view="Button"><Expressions/><location startLine="460" startOffset="8" endLine="465" endOffset="31"/></Target><Target id="@+id/button_default_value" view="Button"><Expressions/><location startLine="476" startOffset="8" endLine="480" endOffset="32"/></Target></Targets></Layout>