// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentNetworkSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextView ApStatusTitle;

  @NonNull
  public final Button btnDisconnectEthernet;

  @NonNull
  public final Button btnEthernetConfig;

  @NonNull
  public final Button btnStartStream;

  @NonNull
  public final Button btnStopStream;

  @NonNull
  public final Button btnToggleAp;

  @NonNull
  public final Button btnToggleWifi;

  @NonNull
  public final RadioButton rbCameraStream;

  @NonNull
  public final RadioButton rbScreenStream;

  @NonNull
  public final RadioGroup rgStreamType;

  @NonNull
  public final Spinner spinnerNetworkInterface;

  @NonNull
  public final TextView staCurrentNetTitle;

  @NonNull
  public final TextView tvApSsid;

  @NonNull
  public final TextView tvApSsidTitle;

  @NonNull
  public final TextView tvApStatus;

  @NonNull
  public final TextView tvCurrentWifi;

  @NonNull
  public final TextView tvEthernetIp;

  @NonNull
  public final TextView tvEthernetStatus;

  @NonNull
  public final TextView tvRtspStatus;

  @NonNull
  public final TextView tvRtspUri;

  @NonNull
  public final TextView tvSTAStatus;

  @NonNull
  public final TextView tvStaLinkStatusTitle;

  private FragmentNetworkSettingsBinding(@NonNull ScrollView rootView,
      @NonNull TextView ApStatusTitle, @NonNull Button btnDisconnectEthernet,
      @NonNull Button btnEthernetConfig, @NonNull Button btnStartStream,
      @NonNull Button btnStopStream, @NonNull Button btnToggleAp, @NonNull Button btnToggleWifi,
      @NonNull RadioButton rbCameraStream, @NonNull RadioButton rbScreenStream,
      @NonNull RadioGroup rgStreamType, @NonNull Spinner spinnerNetworkInterface,
      @NonNull TextView staCurrentNetTitle, @NonNull TextView tvApSsid,
      @NonNull TextView tvApSsidTitle, @NonNull TextView tvApStatus,
      @NonNull TextView tvCurrentWifi, @NonNull TextView tvEthernetIp,
      @NonNull TextView tvEthernetStatus, @NonNull TextView tvRtspStatus,
      @NonNull TextView tvRtspUri, @NonNull TextView tvSTAStatus,
      @NonNull TextView tvStaLinkStatusTitle) {
    this.rootView = rootView;
    this.ApStatusTitle = ApStatusTitle;
    this.btnDisconnectEthernet = btnDisconnectEthernet;
    this.btnEthernetConfig = btnEthernetConfig;
    this.btnStartStream = btnStartStream;
    this.btnStopStream = btnStopStream;
    this.btnToggleAp = btnToggleAp;
    this.btnToggleWifi = btnToggleWifi;
    this.rbCameraStream = rbCameraStream;
    this.rbScreenStream = rbScreenStream;
    this.rgStreamType = rgStreamType;
    this.spinnerNetworkInterface = spinnerNetworkInterface;
    this.staCurrentNetTitle = staCurrentNetTitle;
    this.tvApSsid = tvApSsid;
    this.tvApSsidTitle = tvApSsidTitle;
    this.tvApStatus = tvApStatus;
    this.tvCurrentWifi = tvCurrentWifi;
    this.tvEthernetIp = tvEthernetIp;
    this.tvEthernetStatus = tvEthernetStatus;
    this.tvRtspStatus = tvRtspStatus;
    this.tvRtspUri = tvRtspUri;
    this.tvSTAStatus = tvSTAStatus;
    this.tvStaLinkStatusTitle = tvStaLinkStatusTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNetworkSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNetworkSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_network_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNetworkSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ApStatusTitle;
      TextView ApStatusTitle = ViewBindings.findChildViewById(rootView, id);
      if (ApStatusTitle == null) {
        break missingId;
      }

      id = R.id.btnDisconnectEthernet;
      Button btnDisconnectEthernet = ViewBindings.findChildViewById(rootView, id);
      if (btnDisconnectEthernet == null) {
        break missingId;
      }

      id = R.id.btnEthernetConfig;
      Button btnEthernetConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnEthernetConfig == null) {
        break missingId;
      }

      id = R.id.btnStartStream;
      Button btnStartStream = ViewBindings.findChildViewById(rootView, id);
      if (btnStartStream == null) {
        break missingId;
      }

      id = R.id.btnStopStream;
      Button btnStopStream = ViewBindings.findChildViewById(rootView, id);
      if (btnStopStream == null) {
        break missingId;
      }

      id = R.id.btnToggleAp;
      Button btnToggleAp = ViewBindings.findChildViewById(rootView, id);
      if (btnToggleAp == null) {
        break missingId;
      }

      id = R.id.btnToggleWifi;
      Button btnToggleWifi = ViewBindings.findChildViewById(rootView, id);
      if (btnToggleWifi == null) {
        break missingId;
      }

      id = R.id.rbCameraStream;
      RadioButton rbCameraStream = ViewBindings.findChildViewById(rootView, id);
      if (rbCameraStream == null) {
        break missingId;
      }

      id = R.id.rbScreenStream;
      RadioButton rbScreenStream = ViewBindings.findChildViewById(rootView, id);
      if (rbScreenStream == null) {
        break missingId;
      }

      id = R.id.rgStreamType;
      RadioGroup rgStreamType = ViewBindings.findChildViewById(rootView, id);
      if (rgStreamType == null) {
        break missingId;
      }

      id = R.id.spinnerNetworkInterface;
      Spinner spinnerNetworkInterface = ViewBindings.findChildViewById(rootView, id);
      if (spinnerNetworkInterface == null) {
        break missingId;
      }

      id = R.id.staCurrentNetTitle;
      TextView staCurrentNetTitle = ViewBindings.findChildViewById(rootView, id);
      if (staCurrentNetTitle == null) {
        break missingId;
      }

      id = R.id.tvApSsid;
      TextView tvApSsid = ViewBindings.findChildViewById(rootView, id);
      if (tvApSsid == null) {
        break missingId;
      }

      id = R.id.tvApSsidTitle;
      TextView tvApSsidTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvApSsidTitle == null) {
        break missingId;
      }

      id = R.id.tvApStatus;
      TextView tvApStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvApStatus == null) {
        break missingId;
      }

      id = R.id.tvCurrentWifi;
      TextView tvCurrentWifi = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentWifi == null) {
        break missingId;
      }

      id = R.id.tvEthernetIp;
      TextView tvEthernetIp = ViewBindings.findChildViewById(rootView, id);
      if (tvEthernetIp == null) {
        break missingId;
      }

      id = R.id.tvEthernetStatus;
      TextView tvEthernetStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvEthernetStatus == null) {
        break missingId;
      }

      id = R.id.tvRtspStatus;
      TextView tvRtspStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvRtspStatus == null) {
        break missingId;
      }

      id = R.id.tvRtspUri;
      TextView tvRtspUri = ViewBindings.findChildViewById(rootView, id);
      if (tvRtspUri == null) {
        break missingId;
      }

      id = R.id.tvSTAStatus;
      TextView tvSTAStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvSTAStatus == null) {
        break missingId;
      }

      id = R.id.tvStaLinkStatusTitle;
      TextView tvStaLinkStatusTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvStaLinkStatusTitle == null) {
        break missingId;
      }

      return new FragmentNetworkSettingsBinding((ScrollView) rootView, ApStatusTitle,
          btnDisconnectEthernet, btnEthernetConfig, btnStartStream, btnStopStream, btnToggleAp,
          btnToggleWifi, rbCameraStream, rbScreenStream, rgStreamType, spinnerNetworkInterface,
          staCurrentNetTitle, tvApSsid, tvApSsidTitle, tvApStatus, tvCurrentWifi, tvEthernetIp,
          tvEthernetStatus, tvRtspStatus, tvRtspUri, tvSTAStatus, tvStaLinkStatusTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
