<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_network_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\fragment_network_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_network_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="404" endOffset="12"/></Target><Target id="@+id/tvEthernetStatus" view="TextView"><Expressions/><location startLine="47" startOffset="16" endLine="53" endOffset="51"/></Target><Target id="@+id/tvEthernetIp" view="TextView"><Expressions/><location startLine="70" startOffset="16" endLine="75" endOffset="44"/></Target><Target id="@+id/btnEthernetConfig" view="Button"><Expressions/><location startLine="84" startOffset="16" endLine="90" endOffset="51"/></Target><Target id="@+id/btnDisconnectEthernet" view="Button"><Expressions/><location startLine="92" startOffset="16" endLine="99" endOffset="53"/></Target><Target id="@+id/tvStaLinkStatusTitle" view="TextView"><Expressions/><location startLine="136" startOffset="16" endLine="142" endOffset="51"/></Target><Target id="@+id/tvSTAStatus" view="TextView"><Expressions/><location startLine="144" startOffset="16" endLine="150" endOffset="51"/></Target><Target id="@+id/staCurrentNetTitle" view="TextView"><Expressions/><location startLine="162" startOffset="16" endLine="167" endOffset="44"/></Target><Target id="@+id/tvCurrentWifi" view="TextView"><Expressions/><location startLine="168" startOffset="16" endLine="174" endOffset="52"/></Target><Target id="@+id/btnToggleWifi" view="Button"><Expressions/><location startLine="179" startOffset="12" endLine="186" endOffset="50"/></Target><Target id="@+id/ApStatusTitle" view="TextView"><Expressions/><location startLine="209" startOffset="20" endLine="215" endOffset="55"/></Target><Target id="@+id/tvApStatus" view="TextView"><Expressions/><location startLine="217" startOffset="20" endLine="223" endOffset="48"/></Target><Target id="@+id/tvApSsidTitle" view="TextView"><Expressions/><location startLine="225" startOffset="20" endLine="232" endOffset="55"/></Target><Target id="@+id/tvApSsid" view="TextView"><Expressions/><location startLine="234" startOffset="16" endLine="239" endOffset="44"/></Target><Target id="@+id/btnToggleAp" view="Button"><Expressions/><location startLine="242" startOffset="16" endLine="248" endOffset="45"/></Target><Target id="@+id/tvRtspStatus" view="TextView"><Expressions/><location startLine="281" startOffset="16" endLine="287" endOffset="51"/></Target><Target id="@+id/tvRtspUri" view="TextView"><Expressions/><location startLine="304" startOffset="16" endLine="311" endOffset="46"/></Target><Target id="@+id/spinnerNetworkInterface" view="Spinner"><Expressions/><location startLine="328" startOffset="16" endLine="332" endOffset="21"/></Target><Target id="@+id/rgStreamType" view="RadioGroup"><Expressions/><location startLine="349" startOffset="16" endLine="373" endOffset="28"/></Target><Target id="@+id/rbCameraStream" view="RadioButton"><Expressions/><location startLine="357" startOffset="20" endLine="364" endOffset="56"/></Target><Target id="@+id/rbScreenStream" view="RadioButton"><Expressions/><location startLine="366" startOffset="20" endLine="372" endOffset="56"/></Target><Target id="@+id/btnStartStream" view="Button"><Expressions/><location startLine="383" startOffset="16" endLine="389" endOffset="51"/></Target><Target id="@+id/btnStopStream" view="Button"><Expressions/><location startLine="391" startOffset="16" endLine="398" endOffset="58"/></Target></Targets></Layout>