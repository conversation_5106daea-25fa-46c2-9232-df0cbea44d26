// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class TestdialogSettingsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageButton btnClose;

  @NonNull
  public final FrameLayout contentContainer;

  @NonNull
  public final LinearLayout formatTabContainer;

  @NonNull
  public final TextView itemFormat;

  @NonNull
  public final TextView itemMeasurement;

  @NonNull
  public final TextView itemMisc;

  @NonNull
  public final TextView itemNetwork;

  @NonNull
  public final TextView itemStorage;

  @NonNull
  public final TextView itemVideo;

  @NonNull
  public final LinearLayout measurementTabContainer;

  @NonNull
  public final LinearLayout miscTabContainer;

  @NonNull
  public final LinearLayout networkTabContainer;

  @NonNull
  public final LinearLayout storageTabContainer;

  @NonNull
  public final LinearLayout videoTabContainer;

  private TestdialogSettingsBinding(@NonNull FrameLayout rootView, @NonNull ImageButton btnClose,
      @NonNull FrameLayout contentContainer, @NonNull LinearLayout formatTabContainer,
      @NonNull TextView itemFormat, @NonNull TextView itemMeasurement, @NonNull TextView itemMisc,
      @NonNull TextView itemNetwork, @NonNull TextView itemStorage, @NonNull TextView itemVideo,
      @NonNull LinearLayout measurementTabContainer, @NonNull LinearLayout miscTabContainer,
      @NonNull LinearLayout networkTabContainer, @NonNull LinearLayout storageTabContainer,
      @NonNull LinearLayout videoTabContainer) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.contentContainer = contentContainer;
    this.formatTabContainer = formatTabContainer;
    this.itemFormat = itemFormat;
    this.itemMeasurement = itemMeasurement;
    this.itemMisc = itemMisc;
    this.itemNetwork = itemNetwork;
    this.itemStorage = itemStorage;
    this.itemVideo = itemVideo;
    this.measurementTabContainer = measurementTabContainer;
    this.miscTabContainer = miscTabContainer;
    this.networkTabContainer = networkTabContainer;
    this.storageTabContainer = storageTabContainer;
    this.videoTabContainer = videoTabContainer;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static TestdialogSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TestdialogSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.testdialog_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TestdialogSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_close;
      ImageButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.content_container;
      FrameLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.format_tab_container;
      LinearLayout formatTabContainer = ViewBindings.findChildViewById(rootView, id);
      if (formatTabContainer == null) {
        break missingId;
      }

      id = R.id.item_format;
      TextView itemFormat = ViewBindings.findChildViewById(rootView, id);
      if (itemFormat == null) {
        break missingId;
      }

      id = R.id.item_measurement;
      TextView itemMeasurement = ViewBindings.findChildViewById(rootView, id);
      if (itemMeasurement == null) {
        break missingId;
      }

      id = R.id.item_misc;
      TextView itemMisc = ViewBindings.findChildViewById(rootView, id);
      if (itemMisc == null) {
        break missingId;
      }

      id = R.id.item_network;
      TextView itemNetwork = ViewBindings.findChildViewById(rootView, id);
      if (itemNetwork == null) {
        break missingId;
      }

      id = R.id.item_storage;
      TextView itemStorage = ViewBindings.findChildViewById(rootView, id);
      if (itemStorage == null) {
        break missingId;
      }

      id = R.id.item_video;
      TextView itemVideo = ViewBindings.findChildViewById(rootView, id);
      if (itemVideo == null) {
        break missingId;
      }

      id = R.id.measurement_tab_container;
      LinearLayout measurementTabContainer = ViewBindings.findChildViewById(rootView, id);
      if (measurementTabContainer == null) {
        break missingId;
      }

      id = R.id.misc_tab_container;
      LinearLayout miscTabContainer = ViewBindings.findChildViewById(rootView, id);
      if (miscTabContainer == null) {
        break missingId;
      }

      id = R.id.network_tab_container;
      LinearLayout networkTabContainer = ViewBindings.findChildViewById(rootView, id);
      if (networkTabContainer == null) {
        break missingId;
      }

      id = R.id.storage_tab_container;
      LinearLayout storageTabContainer = ViewBindings.findChildViewById(rootView, id);
      if (storageTabContainer == null) {
        break missingId;
      }

      id = R.id.video_tab_container;
      LinearLayout videoTabContainer = ViewBindings.findChildViewById(rootView, id);
      if (videoTabContainer == null) {
        break missingId;
      }

      return new TestdialogSettingsBinding((FrameLayout) rootView, btnClose, contentContainer,
          formatTabContainer, itemFormat, itemMeasurement, itemMisc, itemNetwork, itemStorage,
          itemVideo, measurementTabContainer, miscTabContainer, networkTabContainer,
          storageTabContainer, videoTabContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
