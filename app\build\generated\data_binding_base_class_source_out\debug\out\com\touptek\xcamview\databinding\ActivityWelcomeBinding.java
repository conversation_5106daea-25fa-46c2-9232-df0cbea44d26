// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWelcomeBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView_;

  @NonNull
  public final ListView listInput;

  @NonNull
  public final RelativeLayout rootView;

  @NonNull
  public final TextView txtTitle;

  private ActivityWelcomeBinding(@NonNull RelativeLayout rootView_, @NonNull ListView listInput,
      @NonNull RelativeLayout rootView, @NonNull TextView txtTitle) {
    this.rootView_ = rootView_;
    this.listInput = listInput;
    this.rootView = rootView;
    this.txtTitle = txtTitle;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView_;
  }

  @NonNull
  public static ActivityWelcomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWelcomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_welcome, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWelcomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.list_input;
      ListView listInput = ViewBindings.findChildViewById(rootView, id);
      if (listInput == null) {
        break missingId;
      }

      RelativeLayout rootView_ = (RelativeLayout) rootView;

      id = R.id.txt_title;
      TextView txtTitle = ViewBindings.findChildViewById(rootView, id);
      if (txtTitle == null) {
        break missingId;
      }

      return new ActivityWelcomeBinding((RelativeLayout) rootView, listInput, rootView_, txtTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
