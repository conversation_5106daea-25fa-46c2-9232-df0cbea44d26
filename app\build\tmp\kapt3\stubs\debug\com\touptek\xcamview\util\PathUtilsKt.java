package com.touptek.xcamview.util;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 2, d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\u001a\u0006\u0010\u0000\u001a\u00020\u0001\u001a\u0006\u0010\u0002\u001a\u00020\u0001\u001a\u0006\u0010\u0003\u001a\u00020\u0001\u00a8\u0006\u0004"}, d2 = {"getStorageDCIMPath", "", "getStoragePicturePath", "getStorageVideoPath", "app_debug"})
public final class PathUtilsKt {
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String getStorageDCIMPath() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String getStoragePicturePath() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String getStorageVideoPath() {
        return null;
    }
}