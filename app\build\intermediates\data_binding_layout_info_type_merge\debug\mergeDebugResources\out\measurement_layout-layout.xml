<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="measurement_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\measurement_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/measurement_layout_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="250" endOffset="14"/></Target><Target id="@+id/btn_angle" view="ImageButton"><Expressions/><location startLine="7" startOffset="4" endLine="16" endOffset="40"/></Target><Target id="@+id/btn_angle_three" view="ImageButton"><Expressions/><location startLine="18" startOffset="4" endLine="27" endOffset="40"/></Target><Target id="@+id/btn_point" view="ImageButton"><Expressions/><location startLine="29" startOffset="4" endLine="38" endOffset="40"/></Target><Target id="@+id/btn_arb_line" view="ImageButton"><Expressions/><location startLine="40" startOffset="4" endLine="49" endOffset="40"/></Target><Target id="@+id/btn_three_line" view="ImageButton"><Expressions/><location startLine="51" startOffset="4" endLine="60" endOffset="40"/></Target><Target id="@+id/btn_horizon_line" view="ImageButton"><Expressions/><location startLine="62" startOffset="4" endLine="71" endOffset="40"/></Target><Target id="@+id/btn_vertical_line" view="ImageButton"><Expressions/><location startLine="73" startOffset="4" endLine="82" endOffset="40"/></Target><Target id="@+id/btn_parallel_line" view="ImageButton"><Expressions/><location startLine="84" startOffset="4" endLine="93" endOffset="40"/></Target><Target id="@+id/btn_three_vertical" view="ImageButton"><Expressions/><location startLine="95" startOffset="4" endLine="104" endOffset="40"/></Target><Target id="@+id/btn_rectangle" view="ImageButton"><Expressions/><location startLine="106" startOffset="4" endLine="115" endOffset="40"/></Target><Target id="@+id/btn_three_rectangle" view="ImageButton"><Expressions/><location startLine="117" startOffset="4" endLine="126" endOffset="40"/></Target><Target id="@+id/btn_ellipse" view="ImageButton"><Expressions/><location startLine="128" startOffset="4" endLine="137" endOffset="40"/></Target><Target id="@+id/btn_five_ellipse" view="ImageButton"><Expressions/><location startLine="139" startOffset="4" endLine="148" endOffset="40"/></Target><Target id="@+id/btn_center_circle" view="ImageButton"><Expressions/><location startLine="150" startOffset="4" endLine="159" endOffset="40"/></Target><Target id="@+id/btn_three_circle" view="ImageButton"><Expressions/><location startLine="161" startOffset="4" endLine="170" endOffset="40"/></Target><Target id="@+id/btn_annulus" view="ImageButton"><Expressions/><location startLine="172" startOffset="4" endLine="181" endOffset="40"/></Target><Target id="@+id/btn_annulus2" view="ImageButton"><Expressions/><location startLine="183" startOffset="4" endLine="192" endOffset="40"/></Target><Target id="@+id/btn_twocircles" view="ImageButton"><Expressions/><location startLine="194" startOffset="4" endLine="203" endOffset="40"/></Target><Target id="@+id/btn_three_twocircles" view="ImageButton"><Expressions/><location startLine="205" startOffset="4" endLine="214" endOffset="40"/></Target><Target id="@+id/btn_arc" view="ImageButton"><Expressions/><location startLine="216" startOffset="4" endLine="225" endOffset="40"/></Target><Target id="@+id/btn_delete_measurement" view="ImageButton"><Expressions/><location startLine="228" startOffset="4" endLine="237" endOffset="40"/></Target><Target id="@+id/btn_calibration" view="ImageButton"><Expressions/><location startLine="239" startOffset="4" endLine="248" endOffset="40"/></Target></Targets></Layout>