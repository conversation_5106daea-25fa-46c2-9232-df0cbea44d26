<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_file_details" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\dialog_file_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_file_details_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="14"/></Target><Target id="@+id/tv_file_name" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="37"/></Target><Target id="@+id/tv_file_size" view="TextView"><Expressions/><location startLine="18" startOffset="4" endLine="24" endOffset="37"/></Target><Target id="@+id/tv_modified_date" view="TextView"><Expressions/><location startLine="26" startOffset="4" endLine="32" endOffset="37"/></Target><Target id="@+id/video_info_group" view="LinearLayout"><Expressions/><location startLine="35" startOffset="4" endLine="72" endOffset="18"/></Target><Target id="@+id/tv_video_duration" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="41"/></Target><Target id="@+id/tv_video_bitrate" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="56" endOffset="41"/></Target><Target id="@+id/tv_video_resolution" view="TextView"><Expressions/><location startLine="58" startOffset="8" endLine="64" endOffset="41"/></Target><Target id="@+id/tv_video_framerate" view="TextView"><Expressions/><location startLine="66" startOffset="8" endLine="71" endOffset="53"/></Target></Targets></Layout>