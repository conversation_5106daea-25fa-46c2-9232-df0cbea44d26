// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Switch;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FlipLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Switch switchHfipTv;

  @NonNull
  public final Switch switchVfipTv;

  private FlipLayoutBinding(@NonNull LinearLayout rootView, @NonNull Switch switchHfipTv,
      @NonNull Switch switchVfipTv) {
    this.rootView = rootView;
    this.switchHfipTv = switchHfipTv;
    this.switchVfipTv = switchVfipTv;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FlipLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FlipLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.flip_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FlipLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.switch_hfip_tv;
      Switch switchHfipTv = ViewBindings.findChildViewById(rootView, id);
      if (switchHfipTv == null) {
        break missingId;
      }

      id = R.id.switch_vfip_tv;
      Switch switchVfipTv = ViewBindings.findChildViewById(rootView, id);
      if (switchVfipTv == null) {
        break missingId;
      }

      return new FlipLayoutBinding((LinearLayout) rootView, switchHfipTv, switchVfipTv);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
