<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#D32F2F" />
            <corners android:radius="10dp" />
            <stroke android:width="2dp" android:color="#B71C1C" />
        </shape>
    </item>

    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#E0E0E0" />
            <corners android:radius="10dp" />
            <stroke android:width="1dp" android:color="#BDBDBD" />
        </shape>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F44336" />
            <corners android:radius="10dp" />
            <stroke android:width="1dp" android:color="#D32F2F" />
        </shape>
    </item>
</selector>
