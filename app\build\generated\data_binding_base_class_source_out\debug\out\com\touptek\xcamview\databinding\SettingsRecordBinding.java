// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SettingsRecordBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnApplySettings;

  @NonNull
  public final Button btnResetDefault;

  @NonNull
  public final RadioGroup modeGroup;

  @NonNull
  public final RadioGroup qualityGroup;

  @NonNull
  public final RadioButton radio1080p;

  @NonNull
  public final RadioButton radio4k;

  @NonNull
  public final RadioButton radioHigh;

  @NonNull
  public final RadioButton radioLow;

  @NonNull
  public final RadioButton radioLowLatency;

  @NonNull
  public final RadioButton radioMedium;

  @NonNull
  public final RadioButton radioNormal;

  @NonNull
  public final RadioGroup resolutionGroup;

  private SettingsRecordBinding(@NonNull ScrollView rootView, @NonNull Button btnApplySettings,
      @NonNull Button btnResetDefault, @NonNull RadioGroup modeGroup,
      @NonNull RadioGroup qualityGroup, @NonNull RadioButton radio1080p,
      @NonNull RadioButton radio4k, @NonNull RadioButton radioHigh, @NonNull RadioButton radioLow,
      @NonNull RadioButton radioLowLatency, @NonNull RadioButton radioMedium,
      @NonNull RadioButton radioNormal, @NonNull RadioGroup resolutionGroup) {
    this.rootView = rootView;
    this.btnApplySettings = btnApplySettings;
    this.btnResetDefault = btnResetDefault;
    this.modeGroup = modeGroup;
    this.qualityGroup = qualityGroup;
    this.radio1080p = radio1080p;
    this.radio4k = radio4k;
    this.radioHigh = radioHigh;
    this.radioLow = radioLow;
    this.radioLowLatency = radioLowLatency;
    this.radioMedium = radioMedium;
    this.radioNormal = radioNormal;
    this.resolutionGroup = resolutionGroup;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static SettingsRecordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SettingsRecordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.settings_record, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SettingsRecordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnApplySettings;
      Button btnApplySettings = ViewBindings.findChildViewById(rootView, id);
      if (btnApplySettings == null) {
        break missingId;
      }

      id = R.id.btnResetDefault;
      Button btnResetDefault = ViewBindings.findChildViewById(rootView, id);
      if (btnResetDefault == null) {
        break missingId;
      }

      id = R.id.mode_group;
      RadioGroup modeGroup = ViewBindings.findChildViewById(rootView, id);
      if (modeGroup == null) {
        break missingId;
      }

      id = R.id.quality_group;
      RadioGroup qualityGroup = ViewBindings.findChildViewById(rootView, id);
      if (qualityGroup == null) {
        break missingId;
      }

      id = R.id.radio_1080p;
      RadioButton radio1080p = ViewBindings.findChildViewById(rootView, id);
      if (radio1080p == null) {
        break missingId;
      }

      id = R.id.radio_4k;
      RadioButton radio4k = ViewBindings.findChildViewById(rootView, id);
      if (radio4k == null) {
        break missingId;
      }

      id = R.id.radio_high;
      RadioButton radioHigh = ViewBindings.findChildViewById(rootView, id);
      if (radioHigh == null) {
        break missingId;
      }

      id = R.id.radio_low;
      RadioButton radioLow = ViewBindings.findChildViewById(rootView, id);
      if (radioLow == null) {
        break missingId;
      }

      id = R.id.radio_low_latency;
      RadioButton radioLowLatency = ViewBindings.findChildViewById(rootView, id);
      if (radioLowLatency == null) {
        break missingId;
      }

      id = R.id.radio_medium;
      RadioButton radioMedium = ViewBindings.findChildViewById(rootView, id);
      if (radioMedium == null) {
        break missingId;
      }

      id = R.id.radio_normal;
      RadioButton radioNormal = ViewBindings.findChildViewById(rootView, id);
      if (radioNormal == null) {
        break missingId;
      }

      id = R.id.resolution_group;
      RadioGroup resolutionGroup = ViewBindings.findChildViewById(rootView, id);
      if (resolutionGroup == null) {
        break missingId;
      }

      return new SettingsRecordBinding((ScrollView) rootView, btnApplySettings, btnResetDefault,
          modeGroup, qualityGroup, radio1080p, radio4k, radioHigh, radioLow, radioLowLatency,
          radioMedium, radioNormal, resolutionGroup);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
