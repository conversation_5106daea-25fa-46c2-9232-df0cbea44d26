// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AutoaeLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox AECheckBox;

  @NonNull
  public final ImageButton btnAddExposureCompensation;

  @NonNull
  public final ImageButton btnAddExposureTime;

  @NonNull
  public final ImageButton btnAddGain;

  @NonNull
  public final Button btnDefaultAe;

  @NonNull
  public final ImageButton btnReduceExposureCompensation;

  @NonNull
  public final ImageButton btnReduceExposureTime;

  @NonNull
  public final ImageButton btnReduceGain;

  @NonNull
  public final SeekBar seekbarCompensationTv;

  @NonNull
  public final SeekBar seekbarExposureTimeTv;

  @NonNull
  public final SeekBar seekbarGainTv;

  @NonNull
  public final TextView textCompensationValue;

  @NonNull
  public final TextView textExposureTimeValue;

  @NonNull
  public final TextView textGainValue;

  private AutoaeLayoutBinding(@NonNull LinearLayout rootView, @NonNull CheckBox AECheckBox,
      @NonNull ImageButton btnAddExposureCompensation, @NonNull ImageButton btnAddExposureTime,
      @NonNull ImageButton btnAddGain, @NonNull Button btnDefaultAe,
      @NonNull ImageButton btnReduceExposureCompensation,
      @NonNull ImageButton btnReduceExposureTime, @NonNull ImageButton btnReduceGain,
      @NonNull SeekBar seekbarCompensationTv, @NonNull SeekBar seekbarExposureTimeTv,
      @NonNull SeekBar seekbarGainTv, @NonNull TextView textCompensationValue,
      @NonNull TextView textExposureTimeValue, @NonNull TextView textGainValue) {
    this.rootView = rootView;
    this.AECheckBox = AECheckBox;
    this.btnAddExposureCompensation = btnAddExposureCompensation;
    this.btnAddExposureTime = btnAddExposureTime;
    this.btnAddGain = btnAddGain;
    this.btnDefaultAe = btnDefaultAe;
    this.btnReduceExposureCompensation = btnReduceExposureCompensation;
    this.btnReduceExposureTime = btnReduceExposureTime;
    this.btnReduceGain = btnReduceGain;
    this.seekbarCompensationTv = seekbarCompensationTv;
    this.seekbarExposureTimeTv = seekbarExposureTimeTv;
    this.seekbarGainTv = seekbarGainTv;
    this.textCompensationValue = textCompensationValue;
    this.textExposureTimeValue = textExposureTimeValue;
    this.textGainValue = textGainValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AutoaeLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AutoaeLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.autoae_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AutoaeLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.AECheckBox;
      CheckBox AECheckBox = ViewBindings.findChildViewById(rootView, id);
      if (AECheckBox == null) {
        break missingId;
      }

      id = R.id.btn_add_exposure_compensation;
      ImageButton btnAddExposureCompensation = ViewBindings.findChildViewById(rootView, id);
      if (btnAddExposureCompensation == null) {
        break missingId;
      }

      id = R.id.btn_add_exposure_time;
      ImageButton btnAddExposureTime = ViewBindings.findChildViewById(rootView, id);
      if (btnAddExposureTime == null) {
        break missingId;
      }

      id = R.id.btn_add_gain;
      ImageButton btnAddGain = ViewBindings.findChildViewById(rootView, id);
      if (btnAddGain == null) {
        break missingId;
      }

      id = R.id.btn_Default_ae;
      Button btnDefaultAe = ViewBindings.findChildViewById(rootView, id);
      if (btnDefaultAe == null) {
        break missingId;
      }

      id = R.id.btn_reduce_exposure_compensation;
      ImageButton btnReduceExposureCompensation = ViewBindings.findChildViewById(rootView, id);
      if (btnReduceExposureCompensation == null) {
        break missingId;
      }

      id = R.id.btn_reduce_exposure_time;
      ImageButton btnReduceExposureTime = ViewBindings.findChildViewById(rootView, id);
      if (btnReduceExposureTime == null) {
        break missingId;
      }

      id = R.id.btn_reduce_gain;
      ImageButton btnReduceGain = ViewBindings.findChildViewById(rootView, id);
      if (btnReduceGain == null) {
        break missingId;
      }

      id = R.id.seekbar_compensation_tv;
      SeekBar seekbarCompensationTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarCompensationTv == null) {
        break missingId;
      }

      id = R.id.seekbar_exposure_time_tv;
      SeekBar seekbarExposureTimeTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarExposureTimeTv == null) {
        break missingId;
      }

      id = R.id.seekbar_gain_tv;
      SeekBar seekbarGainTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarGainTv == null) {
        break missingId;
      }

      id = R.id.text_compensation_value;
      TextView textCompensationValue = ViewBindings.findChildViewById(rootView, id);
      if (textCompensationValue == null) {
        break missingId;
      }

      id = R.id.text_exposure_time_value;
      TextView textExposureTimeValue = ViewBindings.findChildViewById(rootView, id);
      if (textExposureTimeValue == null) {
        break missingId;
      }

      id = R.id.text_gain_value;
      TextView textGainValue = ViewBindings.findChildViewById(rootView, id);
      if (textGainValue == null) {
        break missingId;
      }

      return new AutoaeLayoutBinding((LinearLayout) rootView, AECheckBox,
          btnAddExposureCompensation, btnAddExposureTime, btnAddGain, btnDefaultAe,
          btnReduceExposureCompensation, btnReduceExposureTime, btnReduceGain,
          seekbarCompensationTv, seekbarExposureTimeTv, seekbarGainTv, textCompensationValue,
          textExposureTimeValue, textGainValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
