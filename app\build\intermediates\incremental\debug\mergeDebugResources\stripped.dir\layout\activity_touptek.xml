<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:ignore="ExtraText">

    <!-- Camera Control Panel Title -->
    <!-- Version Information -->
    <!-- Capture and Record Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 串口状态靠左，使用 layout_weight 让它占据剩余空间 -->
        <TextView
            android:id="@+id/text_serial_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="串口状态：待更新"
            android:textSize="16sp"
            android:gravity="start" />

        <!-- 版本信息紧随其后 -->
        <TextView
            android:id="@+id/text_version_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="版本: 1.0.0"
            android:textSize="16sp"
            android:gravity="center"/>
    </LinearLayout>



    <TextView
        android:id="@+id/camera_control_panel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingBottom="16dp"
        android:text="相机控制面板"
        android:textSize="20sp"
        android:textStyle="bold" />

    <!-- Capture and Record Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <Button
            android:id="@+id/button_capture"
            android:layout_width="100dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="捕获" />

        <Button
            android:id="@+id/button_record"
            android:layout_width="100dp"
            android:layout_height="70dp"
            android:text="录像" />
        <!-- Auto Exposure Option -->
    </LinearLayout>



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="自动曝光：" />

        <Switch
            android:id="@+id/switch_auto_exposure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:checked="false" />

    </LinearLayout>

    <!-- Exposure Compensation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="曝光补偿：" />

        <TextView
            android:id="@+id/text_exposure_compensation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_exposure_compensation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="25" />

    <!-- Exposure Time -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="曝光时间：" />

        <TextView
            android:id="@+id/text_exposure_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_exposure_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="1000" />

    <!-- Gain -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="增益：" />

        <TextView
            android:id="@+id/text_gain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_gain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="55" />

    <!-- White Balance -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="白平衡：" />

    <RadioGroup
        android:id="@+id/radio_group_white_balance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/radio_auto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:checked="false"
            android:text="自动" />

        <RadioButton
            android:id="@+id/radio_manual"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:checked="true"
            android:text="手动" />

        <RadioButton
            android:id="@+id/radio_roi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:checked="false"
            android:text="ROI" />
    </RadioGroup>

    <!-- Red Slider -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="红色：" />

        <TextView
            android:id="@+id/text_red"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_red"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="4095" />

    <!-- Green Slider -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="绿色：" />

        <TextView
            android:id="@+id/text_green"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_green"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="4095" />

    <!-- Blue Slider -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="蓝色：" />

        <TextView
            android:id="@+id/text_blue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_blue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="4095" />

    <!-- One-Click White Balance Button -->
    <Button
        android:id="@+id/button_one_click_white_balance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="一键白平衡" />

    <!-- Sharpness -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="锐度：" />

        <TextView
            android:id="@+id/text_sharpness"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_sharpness"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100" />

    <!-- Saturation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="饱和度：" />

        <TextView
            android:id="@+id/text_saturation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_saturation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100" />

    <!-- Hue -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="色调：" />

        <TextView
            android:id="@+id/text_hue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginStart="8dp"
            android:text="0" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekbar_hue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100" />

    <!-- Power Supply Options -->
    <RadioGroup
        android:id="@+id/radio_group_power"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/radio_dc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:checked="true"
            android:text="直流" />

        <RadioButton
            android:id="@+id/radio_ac_50hz"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="交流（50Hz）" />

        <RadioButton
            android:id="@+id/radio_ac_60hz"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="交流（60Hz）" />
    </RadioGroup>

    <!-- Scene Dropdown -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="场景：" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Spinner
            android:id="@+id/spinner_scene"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <Button
            android:id="@+id/button_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="保存" />

        <Button
            android:id="@+id/button_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="删除" />

    </LinearLayout>

    <!-- Default Value Button (Centered) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center">

        <Button
            android:id="@+id/button_default_value"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="默认值" />

    </LinearLayout>
</LinearLayout>