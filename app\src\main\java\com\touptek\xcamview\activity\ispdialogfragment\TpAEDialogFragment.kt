package com.touptek.xcamview.activity.ispdialogfragment
import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageButton
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.video.TpIspParam
import kotlin.collections.forEach
import kotlin.let
import kotlin.ranges.coerceIn

class TpAEDialogFragment : BaseISPDialogFragment(){
    private val handler = Handler(Looper.getMainLooper())
    private val seekBarListeners = mutableMapOf<Int, SeekBar.OnSeekBarChangeListener>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.autoae_layout, container, false)
        initTpIspParam()
        initSeekbar(view)
        initButtonControls(view)
        initParameterValue(view)

        TpIspParam.addOnDataChangedListener(object : TpIspParam.OnDataChangedListener {
            override fun onDataChanged(param: TpIspParam, newValue: Int) {
                handler.post {
                    updateSeekBar(param, newValue) // 调用更新方法
                }
            }

            override fun onLongDataChanged(param: TpIspParam, newValue: Long) {
            }
        })
        return view
    }


    override fun onStart() {
        super.onStart()
        TpIspParam.requestAllParamRanges()
    }

    override fun getDialogHeight(): Int {
        // AE对话框的预估高度：约400dp
        return dpToPx(350)
    }

    private fun initTpIspParam() {
//        TpIspParam.init(context)
//        // 设置串口状态变化监听器
//        TpIspParam.setOnSerialStateChangedListener { connected ->
//            if (connected){
//                TpIspParam.requestAllParamRanges()
//                showToast("串口已连接")
//            }else{
//                showToast("串口断开")
//            }
//        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun initParameterValue(view: View) {
        setSeekBarProgress(
            view,
            R.id.seekbar_compensation_tv,
            R.id.text_compensation_value,
            TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_exposure_time_tv,
            R.id.text_exposure_time_value,
            TpIspParam.TOUPTEK_PARAM_EXPOSURETIME
        )
        setSeekBarProgress(
            view,
            R.id.seekbar_gain_tv,
            R.id.text_gain_value,
            TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN
        )

        // 更新自动曝光 Switch
        @SuppressLint("UseSwitchCompatOrMaterialCode")
        val AECheckBox = view.findViewById<CheckBox>(R.id.AECheckBox)
        val autoExposureValue = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE)
        AECheckBox.isChecked = autoExposureValue == 1 // 1 表示开启，0 表示关闭
        view.post {
            when (autoExposureValue) {
                1 -> {
                    enableSeekBar(view, R.id.seekbar_compensation_tv)
                    disableSeekBar(view, R.id.seekbar_exposure_time_tv)
                    disableSeekBar(view, R.id.seekbar_gain_tv)
                    changeAdjustBtnState(true)
                }

                else -> {
                    disableSeekBar(view, R.id.seekbar_compensation_tv)
                    enableSeekBar(view, R.id.seekbar_exposure_time_tv)
                    enableSeekBar(view, R.id.seekbar_gain_tv)
                    changeAdjustBtnState(false)
                }
            }
        }

        val checkBox = view.findViewById<CheckBox>(R.id.AECheckBox)
        checkBox.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                enableSeekBar(view, R.id.seekbar_compensation_tv)
                disableSeekBar(view, R.id.seekbar_exposure_time_tv)
                disableSeekBar(view, R.id.seekbar_gain_tv)

                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE,1)
                changeAdjustBtnState(true)
            } else {
                disableSeekBar(view, R.id.seekbar_compensation_tv)
                enableSeekBar(view, R.id.seekbar_exposure_time_tv)
                enableSeekBar(view, R.id.seekbar_gain_tv)

                TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE,0)
                changeAdjustBtnState(false)
            }
        }
    }

    private fun disableSeekBar(view: View, seekBarId: Int) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.isEnabled = false
    }


    private fun enableSeekBar(view: View, seekBarId: Int) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.isEnabled = true
    }

    private fun initSeekbar(view: View) {
        setupSeekBar(
            view,
            R.id.seekbar_compensation_tv,
            R.id.text_compensation_value,
            TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION
        )

        setupSeekBar(
            view,
            R.id.seekbar_exposure_time_tv,
            R.id.text_exposure_time_value,
            TpIspParam.TOUPTEK_PARAM_EXPOSURETIME
        )

        setupSeekBar(
            view,
            R.id.seekbar_gain_tv,
            R.id.text_gain_value,
            TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN
        )

    }

    private fun setupSeekBar(view: View, seekBarId: Int, textViewId: Int, param: TpIspParam) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        seekBar.min = TpIspParam.getMinValue(param)
        seekBar.max = TpIspParam.getMaxValue(param)

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                TpIspParam.updateParam(TpIspParam.valueOf(param.name),progress)
                updateSeekBar(param, progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸开始时的逻辑
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                // 可在此处添加触摸结束时的逻辑
            }
        })
    }

    private fun updateSeekBar(param: TpIspParam, newValue: Int) {
        if (view == null) return  // 避免空指针

        var seekBarId = -1
        var textViewId = -1

        when (param) {
            TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION -> {
                seekBarId = R.id.seekbar_compensation_tv
                textViewId = R.id.text_compensation_value
            }

            TpIspParam.TOUPTEK_PARAM_EXPOSURETIME -> {
                seekBarId = R.id.seekbar_exposure_time_tv
                textViewId = R.id.text_exposure_time_value
            }

            TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN -> {
                seekBarId = R.id.seekbar_gain_tv
                textViewId = R.id.text_gain_value
            }
            else -> return
        }

        val rootView = requireView()
        val seekBar = rootView.findViewById<SeekBar>(seekBarId)
        val textView = rootView.findViewById<TextView>(textViewId)

        if (seekBar != null && textView != null) {
            seekBar.progress = newValue
            textView.text = newValue.toString()
        }
    }

    private fun setSeekBarProgress(
        view: View,
        seekBarId: Int,
        textViewId: Int,
        param: TpIspParam
    ) {
        val seekBar = view.findViewById<SeekBar>(seekBarId)
        val textView = view.findViewById<TextView>(textViewId)
        val value = TpIspParam.getCurrentValue(param) // 获取保存的进度值
        seekBar.progress = value // 设置 SeekBar 的进度
        textView.text = value.toString() // 设置 TextView 显示当前值
    }

    private fun handleAEDefaultButtonClick(view: View) {
        // 在这里添加捕获按钮点击时的操作，例如开始捕获图像
        Toast.makeText(activity, "恢复自动曝光默认状态", Toast.LENGTH_SHORT).show()
        setDefaultParameter(view)

    }

    private fun initButtonControls(view: View) {
        // 配置按钮的短按和长按行为
        setupButtonControl(view, R.id.btn_reduce_exposure_compensation, R.id.seekbar_compensation_tv, -1, -1)
        setupButtonControl(view, R.id.btn_add_exposure_compensation, R.id.seekbar_compensation_tv, +1, +1)

        setupButtonControl(view, R.id.btn_reduce_exposure_time, R.id.seekbar_exposure_time_tv, -1, -1)
        setupButtonControl(view, R.id.btn_add_exposure_time, R.id.seekbar_exposure_time_tv, +1, +1)

        setupButtonControl(view, R.id.btn_reduce_gain, R.id.seekbar_gain_tv, -1, -1)
        setupButtonControl(view, R.id.btn_add_gain, R.id.seekbar_gain_tv, +1, +1)

        val buttonAE = view.findViewById<Button>(R.id.btn_Default_ae)
        buttonAE.setOnClickListener { v: View? -> handleAEDefaultButtonClick(view) }

    }

    private fun setupButtonControl(
        view: View,
        buttonId: Int,
        seekBarId: Int,
        shortIncrement: Int,
        longIncrement: Int
    ) {
        val button = view.findViewById<ImageButton>(buttonId)
        // 设置短按点击监听
        button.setOnClickListener {
            setShortPress(seekBarId, shortIncrement)
        }
        // 设置长按持续操作
        setupLongPress(button, seekBarId, longIncrement)
    }

    private fun setShortPress(seekBarId: Int, delta: Int) {
        val seekBar = view?.findViewById<SeekBar>(seekBarId) ?: return
        val newValue = (seekBar.progress + delta).coerceIn(seekBar.min, seekBar.max)

        if (newValue != seekBar.progress) {
            seekBar.progress = newValue
            seekBarListeners[seekBarId]?.onProgressChanged(seekBar, newValue, false)
        }
    }

    private fun setupLongPress(button: ImageButton, seekBarId: Int, delta: Int) {
        var delayRunnable: Runnable? = null
        var periodicRunnable: Runnable? = null
        var isLongPressTriggered = false // 标志是否已触发长按

        button.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isLongPressTriggered = false // 重置标志
                    // 移除之前的回调
                    delayRunnable?.let { handler.removeCallbacks(it) }
                    periodicRunnable?.let { handler.removeCallbacks(it) }

                    // 设置延迟任务：500ms后触发
                    delayRunnable = Runnable {
                        isLongPressTriggered = true // 标记长按已触发
                        periodicRunnable = object : Runnable {
                            override fun run() {
                                setShortPress(seekBarId, delta)
                                handler.postDelayed(this, 100)
                            }
                        }
                        periodicRunnable?.let { handler.post(it) }
                    }
                    handler.postDelayed(delayRunnable!!, 300)
                    false // 不消费事件，允许触发单击
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 移除所有回调
                    delayRunnable?.let { handler.removeCallbacks(it) }
                    periodicRunnable?.let { handler.removeCallbacks(it) }
                    // 根据是否触发长按决定是否消费事件
                    if (isLongPressTriggered) {
                        isLongPressTriggered = false
                        true // 消费事件，阻止单击触发
                    } else {
                        false // 允许单击事件
                    }
                }
                else -> false
            }
        }
    }

    private fun changeAdjustBtnState(enable: Boolean) {
        // 所有调节按钮的ID数组（包含需要反转的按钮）
        val adjustButtonIds = arrayOf(
            R.id.btn_reduce_exposure_compensation,
            R.id.btn_add_exposure_compensation,
            R.id.btn_reduce_exposure_time,
            R.id.btn_add_exposure_time,
            R.id.btn_reduce_gain,
            R.id.btn_add_gain
        )

        view?.let { rootView ->
            adjustButtonIds.forEach { buttonId ->
                // 根据按钮ID决定是否反转状态
                val finalEnable = when (buttonId) {
                    R.id.btn_reduce_exposure_compensation,
                    R.id.btn_add_exposure_compensation -> enable  // 这两个按钮取反
                    else -> !enable                                  // 其他按钮保持原值
                }

                rootView.findViewById<ImageButton>(buttonId)?.isEnabled = finalEnable
            }
        }

    }

    private fun setDefaultParameter(view: View) {
        val AECheckBox = view.findViewById<CheckBox>(R.id.AECheckBox)
        AECheckBox.isChecked = true

        enableSeekBar(view, R.id.seekbar_compensation_tv)
        disableSeekBar(view, R.id.seekbar_exposure_time_tv)
        disableSeekBar(view, R.id.seekbar_gain_tv)
        changeAdjustBtnState(true)
    }
}