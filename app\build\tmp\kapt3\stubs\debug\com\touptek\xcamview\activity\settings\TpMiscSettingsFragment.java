package com.touptek.xcamview.activity.settings;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001:\u0001\u001cB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010\u000f\u001a\u00020\fH\u0002J\b\u0010\u0010\u001a\u00020\fH\u0002J\u0010\u0010\u0011\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J&\u0010\u0014\u001a\u0004\u0018\u00010\u000e2\u0006\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0016J\u001a\u0010\u001b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0016R\u001c\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpMiscSettingsFragment;", "Lcom/touptek/xcamview/util/BaseFragment;", "()V", "modeChangeListener", "Lcom/touptek/xcamview/activity/settings/TpMiscSettingsFragment$OnModeChangeListener;", "getModeChangeListener", "()Lcom/touptek/xcamview/activity/settings/TpMiscSettingsFragment$OnModeChangeListener;", "setModeChangeListener", "(Lcom/touptek/xcamview/activity/settings/TpMiscSettingsFragment$OnModeChangeListener;)V", "modeRadioGroup", "Landroid/widget/RadioGroup;", "createAndApplyBlueSelector", "", "view", "Landroid/view/View;", "handleHighFrameRateMode", "handleLowLatencyMode", "onAttach", "context", "Landroid/content/Context;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "OnModeChangeListener", "app_debug"})
public final class TpMiscSettingsFragment extends com.touptek.xcamview.util.BaseFragment {
    private android.widget.RadioGroup modeRadioGroup;
    @org.jetbrains.annotations.Nullable
    private com.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener modeChangeListener;
    
    public TpMiscSettingsFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener getModeChangeListener() {
        return null;
    }
    
    public final void setModeChangeListener(@org.jetbrains.annotations.Nullable
    com.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onAttach(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
    }
    
    private final void createAndApplyBlueSelector(android.view.View view) {
    }
    
    private final void handleLowLatencyMode() {
    }
    
    private final void handleHighFrameRateMode() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&\u00a8\u0006\u0004"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpMiscSettingsFragment$OnModeChangeListener;", "", "onSwitchToTVMode", "", "app_debug"})
    public static abstract interface OnModeChangeListener {
        
        public abstract void onSwitchToTVMode();
    }
}