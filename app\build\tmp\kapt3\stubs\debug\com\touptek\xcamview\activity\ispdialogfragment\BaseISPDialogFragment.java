package com.touptek.xcamview.activity.ispdialogfragment;

import java.lang.System;

/**
 * ISP对话框基类，提供统一的宽度和位置计算逻辑
 * 对话框宽度：360dp（覆盖4个按钮：btn_exposure → btn_image_processing）
 * 水平对齐：左边缘与btn_exposure对齐，右边缘与btn_image_processing对齐
 * 垂直对齐：底部边缘与TpFlipDialogFragment的底部边缘对齐
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0003\b&\u0018\u0000 \u00122\u00020\u0001:\u0001\u0012B\u0005\u00a2\u0006\u0002\u0010\u0002J<\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\u00052\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u0010\u0010\f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0004J\b\u0010\u000e\u001a\u00020\u0005H$J\b\u0010\u000f\u001a\u00020\u0010H\u0016J\b\u0010\u0011\u001a\u00020\u0010H\u0002\u00a8\u0006\u0013"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/BaseISPDialogFragment;", "Lcom/touptek/xcamview/util/BaseDialogFragment;", "()V", "calculateDialogPosition", "Lkotlin/Pair;", "", "buttonGroupX", "buttonGroupY", "buttonGroupWidth", "dialogWidthPx", "displayMetrics", "Landroid/util/DisplayMetrics;", "dpToPx", "dp", "getDialogHeight", "onStart", "", "setupDialogWindow", "Companion", "app_debug"})
public abstract class BaseISPDialogFragment extends com.touptek.xcamview.util.BaseDialogFragment {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.ispdialogfragment.BaseISPDialogFragment.Companion Companion = null;
    private static final java.lang.String TAG = "BaseISPDialogFragment";
    private static final int BUTTON_GROUP_WIDTH_DP = 360;
    private static final int VERTICAL_MARGIN_DP = 20;
    private static final int FLIP_DIALOG_BOTTOM_OFFSET_DP = 75;
    
    public BaseISPDialogFragment() {
        super();
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void setupDialogWindow() {
    }
    
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateDialogPosition(int buttonGroupX, int buttonGroupY, int buttonGroupWidth, int dialogWidthPx, android.util.DisplayMetrics displayMetrics) {
        return null;
    }
    
    /**
     * 子类需要实现此方法，返回对话框的预估高度（像素）
     * 用于位置计算时的边界检查
     */
    protected abstract int getDialogHeight();
    
    /**
     * 获取标准的对话框高度（dp转像素）
     */
    protected final int dpToPx(int dp) {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/BaseISPDialogFragment$Companion;", "", "()V", "BUTTON_GROUP_WIDTH_DP", "", "FLIP_DIALOG_BOTTOM_OFFSET_DP", "TAG", "", "VERTICAL_MARGIN_DP", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}