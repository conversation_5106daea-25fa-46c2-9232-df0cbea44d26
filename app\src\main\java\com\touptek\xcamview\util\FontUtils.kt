package com.touptek.xcamview.util

import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import com.touptek.xcamview.R

object FontUtils {
    const val FONT_SYSTEM = 0
    const val FONT_SONG = 1
    const val FONT_KAI = 2

    private var currentFont = FONT_SYSTEM
    private var appTypeface: Typeface? = null
    private val activities = mutableListOf<AppCompatActivity>()
    private val dialogFragments = mutableListOf<DialogFragment>()
    private val fragments = mutableListOf<Fragment>()

    fun setAppFont(context: Context, fontType: Int) {
        currentFont = fontType
        appTypeface = when (fontType) {
            FONT_SONG -> getSongTypeface(context)
            FONT_KAI -> getKaiTypeface(context)
            else -> Typeface.DEFAULT
        }
        updateAllActivities()
        updateAllDialogFragments()
        updateAllFragments()
    }

    fun registerActivity(activity: AppCompatActivity) {
        if (!activities.contains(activity)) {
            activities.add(activity)
            applyFontToActivity(activity)
        }
    }

    fun unregisterActivity(activity: AppCompatActivity) {
        activities.remove(activity)
    }

    fun registerDialogFragment(dialogFragment: DialogFragment) {
        if (!dialogFragments.contains(dialogFragment)) {
            dialogFragments.add(dialogFragment)
            applyFontToDialogFragment(dialogFragment)
        }
    }

    fun unregisterDialogFragment(dialogFragment: DialogFragment) {
        dialogFragments.remove(dialogFragment)
    }

    fun registerFragment(fragment: Fragment) {
        if (!fragments.contains(fragment)) {
            fragments.add(fragment)
            applyFontToFragment(fragment)
        }
    }

    fun unregisterFragment(fragment: Fragment) {
        fragments.remove(fragment)
    }

    private fun updateAllActivities() {
        activities.forEach { activity ->
            activity.runOnUiThread {
                applyFontToActivity(activity)
            }
        }
    }

    private fun updateAllDialogFragments() {
        dialogFragments.forEach { dialogFragment ->
            dialogFragment.activity?.runOnUiThread {
                applyFontToDialogFragment(dialogFragment)
            }
        }
    }

    private fun updateAllFragments() {
        fragments.forEach { fragment ->
            fragment.activity?.runOnUiThread {
                applyFontToFragment(fragment)
            }
        }
    }

    fun applyFontToActivity(activity: AppCompatActivity) {
        val rootView = activity.window.decorView.findViewById<View>(android.R.id.content)
        applyFontToViewGroup(rootView)
    }

    fun applyFontToDialogFragment(dialogFragment: DialogFragment) {
        dialogFragment.view?.let { rootView ->
            applyFontToViewGroup(rootView)
        }
    }

    fun applyFontToFragment(fragment: Fragment) {
        fragment.view?.let { rootView ->
            applyFontToViewGroup(rootView)
        }
    }

    private fun applyFontToViewGroup(view: View) {
        when (view) {
            is TextView -> {
                // 立即应用字体，不延迟
                if (view.typeface != appTypeface) {
                    Log.d("FontUtils", "设置字体到TextView: ${view.text}")
                    view.typeface = appTypeface ?: Typeface.DEFAULT
                }
            }
            is ViewGroup -> {
                for (i in 0 until view.childCount) {
                    applyFontToViewGroup(view.getChildAt(i))
                }
            }
        }
    }

    fun getCurrentFontType(): Int = currentFont

    private fun getSongTypeface(context: Context): Typeface {
        return try {
            ResourcesCompat.getFont(context, R.font.song) ?: Typeface.DEFAULT
        } catch (e: Exception) {
            Typeface.DEFAULT
        }
    }

    private fun getKaiTypeface(context: Context): Typeface {
        return try {
            val typeface = ResourcesCompat.getFont(context, R.font.kai)
            Log.d("FontUtils", "楷体字体加载: ${if (typeface != null) "成功" else "失败"}")
            typeface ?: run {
                Log.e("FontUtils", "楷体字体加载返回null")
                Typeface.DEFAULT
            }
        } catch (e: Exception) {
            Log.e("FontUtils", "楷体字体加载异常: ${e.message}")
            Typeface.DEFAULT
        }
    }
}

abstract class BaseActivity : AppCompatActivity() {
    override fun onResume() {
        super.onResume()
        FontUtils.registerActivity(this)
        FontUtils.applyFontToActivity(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        FontUtils.unregisterActivity(this)
    }
}

abstract class BaseDialogFragment : DialogFragment() {
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        FontUtils.registerDialogFragment(this)
        FontUtils.applyFontToDialogFragment(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        FontUtils.unregisterDialogFragment(this)
    }
}

abstract class BaseFragment : Fragment() {
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        FontUtils.registerFragment(this)
        FontUtils.applyFontToFragment(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        FontUtils.unregisterFragment(this)
    }
}