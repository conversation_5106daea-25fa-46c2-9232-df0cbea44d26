<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="settings_misc" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\settings_misc.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/settings_misc_0" view="ScrollView"><Expressions/><location startLine="0" startOffset="0" endLine="91" endOffset="12"/></Target><Target id="@+id/mode_radio_group" view="RadioGroup"><Expressions/><location startLine="29" startOffset="12" endLine="46" endOffset="24"/></Target><Target id="@+id/radio_A" view="RadioButton"><Expressions/><location startLine="35" startOffset="16" endLine="39" endOffset="41"/></Target><Target id="@+id/radio_B" view="RadioButton"><Expressions/><location startLine="41" startOffset="16" endLine="45" endOffset="41"/></Target><Target id="@+id/font_radio_group" view="RadioGroup"><Expressions/><location startLine="65" startOffset="12" endLine="88" endOffset="24"/></Target><Target id="@+id/radio_font_system" view="RadioButton"><Expressions/><location startLine="71" startOffset="16" endLine="75" endOffset="42"/></Target><Target id="@+id/radio_font_song" view="RadioButton"><Expressions/><location startLine="77" startOffset="16" endLine="81" endOffset="38"/></Target><Target id="@+id/radio_font_kai" view="RadioButton"><Expressions/><location startLine="83" startOffset="16" endLine="87" endOffset="38"/></Target></Targets></Layout>