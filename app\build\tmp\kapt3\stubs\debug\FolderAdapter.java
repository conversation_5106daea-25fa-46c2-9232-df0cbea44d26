
import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u001cB)\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0014\u0010\u0006\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0002\u0010\tJ\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004J\b\u0010\u000f\u001a\u00020\u0010H\u0016J\u001c\u0010\u0011\u001a\u00020\b2\n\u0010\u0012\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u0010H\u0016J\u001c\u0010\u0014\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0010H\u0016J\u0018\u0010\u0018\u001a\u00020\b2\u0006\u0010\u0019\u001a\u00020\u000b2\b\u0010\u0015\u001a\u0004\u0018\u00010\u0005J\u001e\u0010\u001a\u001a\u00020\b2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\b\u0010\u0015\u001a\u0004\u0018\u00010\u0005R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0006\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"LFolderAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "LFolderAdapter$ViewHolder;", "folders", "", "Ljava/io/File;", "onFolderSelected", "Lkotlin/Function1;", "", "(Ljava/util/List;Lkotlin/jvm/functions/Function1;)V", "isRootFolder", "", "parentFolder", "showBackToParent", "getFolders", "getItemCount", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setShowBackToParent", "show", "updateFolders", "newFolders", "ViewHolder", "app_debug"})
public final class FolderAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<FolderAdapter.ViewHolder> {
    private java.util.List<? extends java.io.File> folders;
    private final kotlin.jvm.functions.Function1<java.io.File, kotlin.Unit> onFolderSelected = null;
    private boolean showBackToParent = false;
    private java.io.File parentFolder;
    private boolean isRootFolder = true;
    
    public FolderAdapter(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.io.File> folders, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.io.File, kotlin.Unit> onFolderSelected) {
        super();
    }
    
    public final void updateFolders(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.io.File> newFolders, @org.jetbrains.annotations.Nullable
    java.io.File parent) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.io.File> getFolders() {
        return null;
    }
    
    public final void setShowBackToParent(boolean show, @org.jetbrains.annotations.Nullable
    java.io.File parent) {
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public FolderAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    FolderAdapter.ViewHolder holder, int position) {
    }
    
    @java.lang.Override
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\b\u00a8\u0006\u000b"}, d2 = {"LFolderAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(LFolderAdapter;Landroid/view/View;)V", "fileCount", "Landroid/widget/TextView;", "getFileCount", "()Landroid/widget/TextView;", "folderName", "getFolderName", "app_debug"})
    public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView folderName = null;
        @org.jetbrains.annotations.NotNull
        private final android.widget.TextView fileCount = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getFolderName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.widget.TextView getFileCount() {
            return null;
        }
    }
}