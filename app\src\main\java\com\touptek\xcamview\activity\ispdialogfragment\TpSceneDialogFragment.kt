package com.touptek.xcamview.activity.ispdialogfragment
import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.Spinner
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.video.TpIspParam

class TpSceneDialogFragment : BaseISPDialogFragment() {

    private lateinit var sceneSpinner: Spinner
    private lateinit var addButton: Button
    private lateinit var deleteButton: Button
    private lateinit var sceneAdapter: ArrayAdapter<String>
    private var sceneList = mutableListOf<String>()

    companion object {
        private const val TAG = "TpSceneDialogFragment"
        private val SYSTEM_SCENES = listOf("生物", "体视")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.scene_layout, container, false)

        initViews(view)
        setupSpinner()
        setupButtons()

        return view
    }

    private fun initViews(view: View) {
        sceneSpinner = view.findViewById(R.id.scene_spinner)
        addButton = view.findViewById(R.id.btn_add_scene)
        deleteButton = view.findViewById(R.id.btn_delete_scene)
    }

    private fun setupSpinner() {
        // 获取场景列表
        refreshSceneList()

        // 设置适配器
        sceneAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, sceneList)
        sceneAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        sceneSpinner.adapter = sceneAdapter

        // 设置选择监听器
        sceneSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val selectedScene = sceneList[position]
                Log.d(TAG, "选择场景: $selectedScene")

                // 应用场景
                applyScene(selectedScene)

                // 更新删除按钮状态
                updateDeleteButtonState(selectedScene)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }

    private fun setupButtons() {
        // 添加场景按钮
        addButton.setOnClickListener {
            showAddSceneDialog()
        }

        // 删除场景按钮
        deleteButton.setOnClickListener {
            showDeleteSceneDialog()
        }
    }

    private fun showAddSceneDialog() {
        val editText = EditText(requireContext()).apply {
            hint = "请输入场景名称"
            setPadding(50, 30, 50, 30)
        }

        AlertDialog.Builder(requireContext())
            .setTitle("添加新场景")
            .setMessage("输入场景名称，将保存当前设置为新场景")
            .setView(editText)
            .setPositiveButton("确认") { _, _ ->
                val sceneName = editText.text.toString().trim()
                if (sceneName.isNotEmpty()) {
                    addNewScene(sceneName)
                } else {
                    Toast.makeText(requireContext(), "场景名称不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showDeleteSceneDialog() {
        val selectedScene = getCurrentSelectedScene()
        if (selectedScene.isEmpty()) {
            Toast.makeText(requireContext(), "请先选择要删除的场景", Toast.LENGTH_SHORT).show()
            return
        }

        AlertDialog.Builder(requireContext())
            .setTitle("删除场景")
            .setMessage("删除该场景后，将自动切换至生物场景，是否确认删除？")
            .setPositiveButton("确认") { _, _ ->
                deleteScene(selectedScene)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun addNewScene(sceneName: String) {
        try {
            // 检查场景名是否已存在
            if (sceneList.contains(sceneName)) {
                Toast.makeText(requireContext(), "场景名称已存在", Toast.LENGTH_SHORT).show()
                return
            }

            // 保存当前设置为新场景
            TpIspParam.saveCurrentAsScene(sceneName)
            Log.d(TAG, "保存新场景: $sceneName")

            // 刷新场景列表
            refreshSceneList()
            sceneAdapter.notifyDataSetChanged()

            // 设置为当前选中的场景
            val position = sceneList.indexOf(sceneName)
            if (position >= 0) {
                sceneSpinner.setSelection(position)
            }

            Toast.makeText(requireContext(), "场景 '$sceneName' 添加成功", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "添加场景失败", e)
            Toast.makeText(requireContext(), "添加场景失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun deleteScene(sceneName: String) {
        try {
            // 删除场景
            TpIspParam.deleteScene(sceneName)
            Log.d(TAG, "删除场景: $sceneName")

            // 刷新场景列表
            refreshSceneList()
            sceneAdapter.notifyDataSetChanged()

            // 自动切换到生物场景
            val biologicalPosition = sceneList.indexOf("生物")
            if (biologicalPosition >= 0) {
                sceneSpinner.setSelection(biologicalPosition)
                applyScene("生物")
            }

            Toast.makeText(requireContext(), "场景 '$sceneName' 删除成功", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "删除场景失败", e)
            Toast.makeText(requireContext(), "删除场景失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyScene(sceneName: String) {
        try {
            TpIspParam.applyScene(sceneName)
            Log.d(TAG, "应用场景: $sceneName")
        } catch (e: Exception) {
            Log.e(TAG, "应用场景失败", e)
            Toast.makeText(requireContext(), "应用场景失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun refreshSceneList() {
        sceneList.clear()
        sceneList.addAll(TpIspParam.getAllSceneNames())
        Log.d(TAG, "刷新场景列表: $sceneList")
    }

    private fun getCurrentSelectedScene(): String {
        val position = sceneSpinner.selectedItemPosition
        return if (position >= 0 && position < sceneList.size) {
            sceneList[position]
        } else {
            ""
        }
    }

    private fun updateDeleteButtonState(sceneName: String) {
        // 系统预设场景（生物、体视）不允许删除
        val isSystemScene = SYSTEM_SCENES.contains(sceneName)
        deleteButton.isEnabled = !isSystemScene

        if (isSystemScene) {
            deleteButton.alpha = 0.5f
        } else {
            deleteButton.alpha = 1.0f
        }

        Log.d(TAG, "更新删除按钮状态: $sceneName, 是否系统场景: $isSystemScene")
    }

    override fun onStart() {
        super.onStart()
    }

    override fun getDialogHeight(): Int {
        // 场景管理对话框的预估高度：约250dp
        return dpToPx(260)
    }



}