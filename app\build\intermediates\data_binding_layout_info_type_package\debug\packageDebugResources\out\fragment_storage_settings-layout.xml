<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_storage_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\fragment_storage_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_storage_settings_0" view="ScrollView"><Expressions/><location startLine="0" startOffset="4" endLine="435" endOffset="12"/></Target><Target id="@+id/storage_location_group" view="RadioGroup"><Expressions/><location startLine="27" startOffset="12" endLine="45" endOffset="24"/></Target><Target id="@+id/radio_external" view="RadioButton"><Expressions/><location startLine="33" startOffset="16" endLine="38" endOffset="54"/></Target><Target id="@+id/radio_internal" view="RadioButton"><Expressions/><location startLine="40" startOffset="16" endLine="44" endOffset="44"/></Target><Target id="@+id/cb_smb_enable" view="CheckBox"><Expressions/><location startLine="72" startOffset="12" endLine="77" endOffset="51"/></Target><Target id="@+id/et_server_ip" view="EditText"><Expressions/><location startLine="95" startOffset="20" endLine="109" endOffset="53"/></Target><Target id="@+id/et_share_name" view="EditText"><Expressions/><location startLine="128" startOffset="20" endLine="142" endOffset="53"/></Target><Target id="@+id/et_username" view="EditText"><Expressions/><location startLine="161" startOffset="20" endLine="175" endOffset="53"/></Target><Target id="@+id/et_password" view="EditText"><Expressions/><location startLine="194" startOffset="20" endLine="209" endOffset="53"/></Target><Target id="@+id/btn_test_connection" view="Button"><Expressions/><location startLine="211" startOffset="20" endLine="221" endOffset="49"/></Target><Target id="@+id/sp_remote_path" view="Spinner"><Expressions/><location startLine="240" startOffset="20" endLine="249" endOffset="50"/></Target><Target id="@+id/btn_browse" view="Button"><Expressions/><location startLine="251" startOffset="20" endLine="261" endOffset="48"/></Target><Target id="@+id/tv_upload_status" view="TextView"><Expressions/><location startLine="280" startOffset="20" endLine="288" endOffset="61"/></Target><Target id="@+id/tv_last_upload" view="TextView"><Expressions/><location startLine="306" startOffset="20" endLine="314" endOffset="61"/></Target><Target id="@+id/cb_video_time_suffix" view="CheckBox"><Expressions/><location startLine="360" startOffset="16" endLine="365" endOffset="54"/></Target><Target id="@+id/et_video_prefix" view="EditText"><Expressions/><location startLine="379" startOffset="20" endLine="385" endOffset="46"/></Target><Target id="@+id/cb_image_time_suffix" view="CheckBox"><Expressions/><location startLine="405" startOffset="16" endLine="410" endOffset="54"/></Target><Target id="@+id/et_image_prefix" view="EditText"><Expressions/><location startLine="424" startOffset="20" endLine="430" endOffset="46"/></Target></Targets></Layout>