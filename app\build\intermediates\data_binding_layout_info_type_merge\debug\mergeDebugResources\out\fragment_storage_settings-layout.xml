<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_storage_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\fragment_storage_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_storage_settings_0" view="ScrollView"><Expressions/><location startLine="0" startOffset="4" endLine="443" endOffset="12"/></Target><Target id="@+id/storage_location_group" view="RadioGroup"><Expressions/><location startLine="27" startOffset="12" endLine="45" endOffset="24"/></Target><Target id="@+id/radio_external" view="RadioButton"><Expressions/><location startLine="33" startOffset="16" endLine="38" endOffset="54"/></Target><Target id="@+id/radio_internal" view="RadioButton"><Expressions/><location startLine="40" startOffset="16" endLine="44" endOffset="44"/></Target><Target id="@+id/cb_smb_enable" view="CheckBox"><Expressions/><location startLine="65" startOffset="12" endLine="70" endOffset="51"/></Target><Target id="@+id/et_server_ip" view="EditText"><Expressions/><location startLine="101" startOffset="24" endLine="115" endOffset="57"/></Target><Target id="@+id/et_share_name" view="EditText"><Expressions/><location startLine="134" startOffset="24" endLine="148" endOffset="57"/></Target><Target id="@+id/et_username" view="EditText"><Expressions/><location startLine="167" startOffset="24" endLine="181" endOffset="57"/></Target><Target id="@+id/et_password" view="EditText"><Expressions/><location startLine="200" startOffset="24" endLine="215" endOffset="57"/></Target><Target id="@+id/btn_test_connection" view="Button"><Expressions/><location startLine="217" startOffset="24" endLine="227" endOffset="53"/></Target><Target id="@+id/sp_remote_path" view="Spinner"><Expressions/><location startLine="246" startOffset="24" endLine="255" endOffset="54"/></Target><Target id="@+id/btn_browse" view="Button"><Expressions/><location startLine="257" startOffset="24" endLine="267" endOffset="52"/></Target><Target id="@+id/tv_upload_status" view="TextView"><Expressions/><location startLine="286" startOffset="24" endLine="294" endOffset="65"/></Target><Target id="@+id/tv_last_upload" view="TextView"><Expressions/><location startLine="312" startOffset="24" endLine="320" endOffset="65"/></Target><Target id="@+id/cb_video_time_suffix" view="CheckBox"><Expressions/><location startLine="368" startOffset="16" endLine="373" endOffset="54"/></Target><Target id="@+id/et_video_prefix" view="EditText"><Expressions/><location startLine="387" startOffset="20" endLine="393" endOffset="46"/></Target><Target id="@+id/cb_image_time_suffix" view="CheckBox"><Expressions/><location startLine="413" startOffset="16" endLine="418" endOffset="54"/></Target><Target id="@+id/et_image_prefix" view="EditText"><Expressions/><location startLine="432" startOffset="20" endLine="438" endOffset="46"/></Target></Targets></Layout>