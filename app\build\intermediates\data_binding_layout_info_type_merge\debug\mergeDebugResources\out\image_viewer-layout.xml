<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_viewer" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\image_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/image_viewer_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="106" endOffset="13"/></Target><Target id="@+id/image_view" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="6" startOffset="4" endLine="9" endOffset="46"/></Target><Target id="@+id/image_info_label" view="LinearLayout"><Expressions/><location startLine="12" startOffset="4" endLine="50" endOffset="18"/></Target><Target id="@+id/tv_file_name" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="39" endOffset="43"/></Target><Target id="@+id/tv_resolution" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="49" endOffset="43"/></Target><Target id="@+id/button_panel" view="LinearLayout"><Expressions/><location startLine="53" startOffset="4" endLine="105" endOffset="18"/></Target><Target id="@+id/btn_previous" view="ImageButton"><Expressions/><location startLine="69" startOffset="8" endLine="78" endOffset="37"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="81" startOffset="8" endLine="91" endOffset="37"/></Target><Target id="@+id/btn_next" view="ImageButton"><Expressions/><location startLine="94" startOffset="8" endLine="103" endOffset="37"/></Target></Targets></Layout>