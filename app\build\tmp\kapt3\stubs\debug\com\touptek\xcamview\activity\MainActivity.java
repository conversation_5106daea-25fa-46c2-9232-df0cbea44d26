package com.touptek.xcamview.activity;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u00bc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0003\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003B\u0005\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010.\u001a\u00020/J\u0006\u00100\u001a\u00020/J\u0006\u00101\u001a\u00020/J\u0006\u00102\u001a\u00020/J\b\u00103\u001a\u00020\u0012H\u0002J\u0010\u00104\u001a\u0004\u0018\u0001052\u0006\u00106\u001a\u000207J\u000e\u00108\u001a\u00020/2\u0006\u00109\u001a\u00020\bJ\u0010\u0010:\u001a\u00020\u00122\u0006\u0010;\u001a\u00020\bH\u0002J\u0006\u0010<\u001a\u00020/J\u0006\u0010=\u001a\u00020/J\u0010\u0010>\u001a\u00020/2\u0006\u0010?\u001a\u00020@H\u0002J\u0012\u0010A\u001a\u00020\u00122\b\u0010B\u001a\u0004\u0018\u00010@H\u0016J\n\u0010C\u001a\u0004\u0018\u00010DH\u0002J$\u0010E\u001a\u00020/2\u0006\u0010F\u001a\u00020\b2\u0006\u0010G\u001a\u00020\b2\n\b\u0002\u0010H\u001a\u0004\u0018\u00010IH\u0002J\b\u0010J\u001a\u00020/H\u0002J\b\u0010K\u001a\u00020/H\u0002J\u0010\u0010L\u001a\u00020/2\u0006\u0010M\u001a\u00020\u0012H\u0002J\u0018\u0010N\u001a\u00020\u00122\u0006\u0010O\u001a\u00020P2\u0006\u0010?\u001a\u00020@H\u0002J\u0006\u0010Q\u001a\u00020/J\u0006\u0010R\u001a\u00020/J\b\u0010S\u001a\u00020/H\u0002J\u0006\u0010T\u001a\u00020/J\b\u0010U\u001a\u00020/H\u0002J\b\u0010V\u001a\u00020/H\u0002J\b\u0010W\u001a\u00020/H\u0002J\b\u0010X\u001a\u00020/H\u0002J\b\u0010Y\u001a\u00020/H\u0002J\u0006\u0010Z\u001a\u00020/J\b\u0010[\u001a\u00020/H\u0002J\b\u0010\\\u001a\u00020/H\u0002J\u0006\u0010]\u001a\u00020\u0012J\u0006\u0010^\u001a\u00020\u0012J\u0006\u0010\u0019\u001a\u00020\u0012J\u0006\u0010\u001a\u001a\u00020\u0012J\u0006\u0010_\u001a\u00020\u0012J\u0012\u0010`\u001a\u00020/2\b\u0010a\u001a\u0004\u0018\u00010bH\u0014J\b\u0010c\u001a\u00020/H\u0014J\b\u0010d\u001a\u00020/H\u0016J\b\u0010e\u001a\u00020/H\u0014J\b\u0010f\u001a\u00020/H\u0014J\b\u0010g\u001a\u00020/H\u0016J\u0010\u0010h\u001a\u00020/2\u0006\u0010O\u001a\u00020PH\u0016J\u0010\u0010i\u001a\u00020/2\u0006\u0010O\u001a\u00020PH\u0016J\b\u0010j\u001a\u00020/H\u0002J\u0010\u0010k\u001a\u00020/2\u0006\u0010l\u001a\u000205H\u0002J\u000e\u0010m\u001a\u00020/2\u0006\u0010n\u001a\u00020oJ\b\u0010p\u001a\u00020/H\u0002J\b\u0010q\u001a\u00020/H\u0002J\b\u0010r\u001a\u00020/H\u0002J\b\u0010s\u001a\u00020/H\u0002J\u0006\u0010t\u001a\u00020/J\u0006\u0010u\u001a\u00020/J\b\u0010v\u001a\u00020/H\u0002J\u0006\u0010w\u001a\u00020/J\u0010\u0010x\u001a\u00020/2\u0006\u0010G\u001a\u00020\bH\u0002J\u0006\u0010y\u001a\u00020/J\b\u0010z\u001a\u00020/H\u0002J\u0006\u0010{\u001a\u00020/J\b\u0010|\u001a\u00020/H\u0002J\b\u0010}\u001a\u00020/H\u0002J\u0010\u0010~\u001a\u00020/2\u0006\u0010\u007f\u001a\u00020!H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010(\u001a\u0004\u0018\u00010)X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020+X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010,\u001a\u0004\u0018\u00010-X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0080\u0001"}, d2 = {"Lcom/touptek/xcamview/activity/MainActivity;", "Lcom/touptek/xcamview/util/BaseActivity;", "Landroid/view/View$OnAttachStateChangeListener;", "Lcom/touptek/xcamview/activity/MainMenu$OnRectangleVisibilityListener;", "()V", "MOVE_THRESHOLD", "", "TAG", "", "banner", "Lcom/touptek/xcamview/activity/StatusBanner;", "binding", "Lcom/touptek/xcamview/databinding/ActivityMainBinding;", "gestureDetector", "Landroid/view/GestureDetector;", "handler", "Landroid/os/Handler;", "hasMovedSignificantly", "", "hasPerformedZoom", "initialX", "initialY", "isActivityResumed", "isCameraOpened", "isHDMIConnected", "isMenuEnabled", "isRecording", "isSerialConnected", "rectangleOverlayView", "Lcom/touptek/xcamview/activity/ispdialogfragment/wbroimanagement/TpRectangleOverlayView;", "scaleGestureDetector", "Landroid/view/ScaleGestureDetector;", "startTime", "", "touchCount", "", "touchListener", "Landroid/view/View$OnTouchListener;", "tpHdmiMonitor", "Lcom/touptek/utils/TpHdmiMonitor;", "tpRoiView", "Lcom/touptek/ui/TpRoiView;", "updateTimeRunnable", "Ljava/lang/Runnable;", "videoSystem", "Lcom/touptek/video/TpVideoSystem;", "ActivityCaptureImage", "", "SetZoomIn", "SetZoomOut", "TvModeToCameraMode", "canTriggerMenu", "captureDialogFragment", "Landroid/graphics/Bitmap;", "dialogFragment", "Landroidx/fragment/app/DialogFragment;", "captureImage", "outputPath", "checkVideoSystemReady", "operationName", "clearMeasurements", "createStorageDefaultPath", "debugTouchEvent", "event", "Landroid/view/MotionEvent;", "dispatchTouchEvent", "ev", "getMainMenuFragment", "Lcom/touptek/xcamview/activity/MainMenu;", "handleError", "tag", "message", "throwable", "", "handleHdmiConnected", "handleHdmiDisconnected", "handleHdmiStatusChange", "isConnected", "handleTouchEvent", "v", "Landroid/view/View;", "hideMeasurementView", "hideRectangleOverlay", "initCameraMemoryParameter", "initCameraMode", "initHdmiService", "initISP", "initPanGestureDetector", "initScaleGestureDetector", "initStorageMonitor", "initTVMode", "initTouchEvent", "initializeRoiComponents", "isActivityForeground", "isMeasurementVisible", "isRectangleVisible", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onHideRectangle", "onPause", "onResume", "onShowRectangle", "onViewAttachedToWindow", "onViewDetachedFromWindow", "releaseResources", "saveBitmapToStorage", "bitmap", "setMeasurementMode", "mode", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Mode;", "setupRectangleOverlay", "setupTpVideoSystemTvMode", "setupVideoSystemListener", "setupViewListener", "showBrowseActivity", "showMeasurementView", "showMenu", "showRectangleOverlay", "showToast", "startRecord", "startTimer", "stopRecord", "takeScreenshot", "updateMainMenuRecordButton", "updateTimerText", "duration", "app_debug"})
public final class MainActivity extends com.touptek.xcamview.util.BaseActivity implements android.view.View.OnAttachStateChangeListener, com.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener {
    private com.touptek.xcamview.databinding.ActivityMainBinding binding;
    private boolean isActivityResumed = false;
    private boolean isSerialConnected = false;
    private boolean isHDMIConnected = false;
    private final java.lang.String TAG = "MainActivity";
    private com.touptek.utils.TpHdmiMonitor tpHdmiMonitor;
    private boolean isMenuEnabled = true;
    private boolean isCameraOpened = false;
    private long startTime = 0L;
    private final android.os.Handler handler = null;
    private java.lang.Runnable updateTimeRunnable;
    private com.touptek.xcamview.activity.StatusBanner banner;
    private boolean isRecording = false;
    private android.view.ScaleGestureDetector scaleGestureDetector;
    private android.view.GestureDetector gestureDetector;
    private float initialX = 0.0F;
    private float initialY = 0.0F;
    private int touchCount = 0;
    private boolean hasMovedSignificantly = false;
    private boolean hasPerformedZoom = false;
    private final float MOVE_THRESHOLD = 20.0F;
    private com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView rectangleOverlayView;
    private com.touptek.ui.TpRoiView tpRoiView;
    private com.touptek.video.TpVideoSystem videoSystem;
    private final android.view.View.OnTouchListener touchListener = null;
    
    public MainActivity() {
        super();
    }
    
    public final boolean isMenuEnabled() {
        return false;
    }
    
    private final com.touptek.xcamview.activity.MainMenu getMainMenuFragment() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 设置 TpVideoSystem 监听器（按照 VideoTest 最佳实践）
     */
    private final void setupVideoSystemListener() {
    }
    
    /**
     * 为 TpVideoSystem 设置 TV 模式支持（按照 VideoTest 最佳实践）
     */
    private final void setupTpVideoSystemTvMode() {
    }
    
    @java.lang.Override
    public void onViewAttachedToWindow(@org.jetbrains.annotations.NotNull
    android.view.View v) {
    }
    
    @java.lang.Override
    public void onViewDetachedFromWindow(@org.jetbrains.annotations.NotNull
    android.view.View v) {
    }
    
    /**
     * 初始化 HDMI 服务（按照 VideoTest 最佳实践）
     */
    private final void initHdmiService() {
    }
    
    /**
     * 处理 HDMI 状态变化（按照 VideoTest 最佳实践）
     */
    private final void handleHdmiStatusChange(boolean isConnected) {
    }
    
    /**
     * 处理 HDMI 连接事件
     */
    private final void handleHdmiConnected() {
    }
    
    /**
     * 处理 HDMI 断开事件
     */
    private final void handleHdmiDisconnected() {
    }
    
    private final void showMenu() {
    }
    
    @java.lang.Override
    public boolean dispatchTouchEvent(@org.jetbrains.annotations.Nullable
    android.view.MotionEvent ev) {
        return false;
    }
    
    /**
     * 打开浏览器界面（按照 VideoTest 最佳实践）
     */
    public final void showBrowseActivity() {
    }
    
    public final void SetZoomIn() {
    }
    
    public final void SetZoomOut() {
    }
    
    /**
     * 捕获图像（使用 TpVideoSystem 统一管理）
     */
    public final void ActivityCaptureImage() {
    }
    
    public final void initCameraMode() {
    }
    
    /**
     * 切换到TV模式（使用 TpVideoSystem 统一管理）
     */
    public final void initTVMode() {
    }
    
    private final void debugTouchEvent(android.view.MotionEvent event) {
    }
    
    /**
     * 从TV模式切换回相机模式（使用 TpVideoSystem 统一管理）
     */
    public final void TvModeToCameraMode() {
    }
    
    /**
     * 获取录制状态（使用 TpVideoSystem 统一管理）
     */
    public final boolean isRecording() {
        return false;
    }
    
    /**
     * 开始录制（使用 TpVideoSystem 统一管理）
     */
    public final void startRecord() {
    }
    
    /**
     * 停止录制（使用 TpVideoSystem 统一管理）
     */
    public final void stopRecord() {
    }
    
    private final void updateMainMenuRecordButton() {
    }
    
    /**
     * 设置 TextureView Surface 监听器（按照 VideoTest 最佳实践）
     */
    private final void setupViewListener() {
    }
    
    /**
     * 释放资源（使用 TpVideoSystem 统一管理）
     */
    private final void releaseResources() {
    }
    
    private final void startTimer() {
    }
    
    private final void updateTimerText(long duration) {
    }
    
    @java.lang.Override
    protected void onResume() {
    }
    
    @java.lang.Override
    protected void onPause() {
    }
    
    @java.lang.Override
    protected void onDestroy() {
    }
    
    public final boolean isActivityForeground() {
        return false;
    }
    
    /**
     * 统一的错误处理方法
     */
    private final void handleError(java.lang.String tag, java.lang.String message, java.lang.Throwable throwable) {
    }
    
    /**
     * 检查 TpVideoSystem 状态的统一方法
     */
    private final boolean checkVideoSystemReady(java.lang.String operationName) {
        return false;
    }
    
    private final void initStorageMonitor() {
    }
    
    public final void createStorageDefaultPath() {
    }
    
    private final void initScaleGestureDetector() {
    }
    
    private final void initPanGestureDetector() {
    }
    
    private final void setupRectangleOverlay() {
    }
    
    private final void initTouchEvent() {
    }
    
    @java.lang.Override
    public void onShowRectangle() {
    }
    
    @java.lang.Override
    public void onHideRectangle() {
    }
    
    public final void showMeasurementView() {
    }
    
    public final void hideMeasurementView() {
    }
    
    public final void setMeasurementMode(@org.jetbrains.annotations.NotNull
    com.touptek.xcamview.view.MeasurementOverlayView.Mode mode) {
    }
    
    public final void clearMeasurements() {
    }
    
    public final boolean isMeasurementVisible() {
        return false;
    }
    
    public final void showRectangleOverlay() {
    }
    
    public final void hideRectangleOverlay() {
    }
    
    public final boolean isRectangleVisible() {
        return false;
    }
    
    private final void initISP() {
    }
    
    private final void initCameraMemoryParameter() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void initializeRoiComponents() {
    }
    
    private final boolean handleTouchEvent(android.view.View v, android.view.MotionEvent event) {
        return false;
    }
    
    private final boolean canTriggerMenu() {
        return false;
    }
    
    private final void takeScreenshot() {
    }
    
    /**
     * 保存Bitmap到外部存储
     */
    private final void saveBitmapToStorage(android.graphics.Bitmap bitmap) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.Bitmap captureDialogFragment(@org.jetbrains.annotations.NotNull
    androidx.fragment.app.DialogFragment dialogFragment) {
        return null;
    }
    
    public final void captureImage(@org.jetbrains.annotations.NotNull
    java.lang.String outputPath) {
    }
}