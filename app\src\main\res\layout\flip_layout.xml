<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/rounded_border"
    android:layout_margin="8dp">

    <!-- 第一个 Switch -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <Switch
                android:id="@+id/switch_hfip_tv"
                android:layout_width="wrap_content"
                android:layout_height="46dp"
                android:layout_weight="1"
                android:text="Horizontal"
                android:textColor="@color/colorISPText"
                android:thumb="@drawable/thumb_blue_selector"/>

            <!-- 其他元素保持不变 -->
        </LinearLayout>
    </LinearLayout>

    <!-- 第二个 Switch -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <Switch
                android:id="@+id/switch_vfip_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Vertical"
                android:textColor="@color/colorISPText"
                android:thumb="@drawable/thumb_blue_selector"/>
            <!-- 其他元素保持不变 -->
        </LinearLayout>
    </LinearLayout>
</LinearLayout>