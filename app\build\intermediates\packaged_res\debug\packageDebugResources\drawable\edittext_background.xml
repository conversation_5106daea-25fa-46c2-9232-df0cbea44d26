<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#FAFAFA" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="2dp" 
                android:color="@color/blue_500" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F8F8F8" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="1dp" 
                android:color="#DDDDDD" />
        </shape>
    </item>
    
</selector>
